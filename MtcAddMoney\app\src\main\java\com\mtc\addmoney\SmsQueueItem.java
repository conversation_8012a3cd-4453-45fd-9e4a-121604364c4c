package com.mtc.addmoney;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

/**
 * Represents an SMS or webhook item in the queue
 */
public class SmsQueueItem {
    
    // Item types
    public static final String TYPE_SMS = "sms";
    public static final String TYPE_WEBHOOK = "webhook";
    public static final String TYPE_RECEIVED_SMS = "received_sms";
    
    // Status constants
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_SENT = "sent";
    public static final String STATUS_FAILED = "failed";
    public static final String STATUS_RETRY = "retry";
    
    // JSON keys for storage
    private static final String KEY_TYPE = "type";
    private static final String KEY_RECIPIENT = "recipient";
    private static final String KEY_MESSAGE = "message";
    private static final String KEY_STATUS = "status";
    private static final String KEY_TIMESTAMP = "timestamp";
    private static final String KEY_RETRY_COUNT = "retry_count";
    private static final String KEY_WEBHOOK_URL = "webhook_url";
    private static final String KEY_WEBHOOK_HEADERS = "webhook_headers";
    private static final String KEY_WEBHOOK_IGNORE_SSL = "webhook_ignore_ssl";
    private static final String KEY_LOG_ID = "log_id";
    private static final String KEY_SENDER = "sender";
    private static final String KEY_SENT_TIME = "sent_time";
    private static final String KEY_RECEIVED_TIME = "received_time";
    private static final String KEY_SIM_INFO = "sim_info";
    
    private final Context context;
    private String id;
    private String type;
    private String recipient;
    private String message;
    private String status;
    private long timestamp;
    private int retryCount;
    private String webhookUrl;
    private String webhookHeaders;
    private boolean webhookIgnoreSsl;
    private String logId;
    private String sender;
    private long sentTime;
    private long receivedTime;
    private String simInfo;
    
    public SmsQueueItem(Context context) {
        this.context = context;
        this.id = UUID.randomUUID().toString();
        this.timestamp = System.currentTimeMillis();
        this.status = STATUS_PENDING;
        this.retryCount = 0;
        this.webhookIgnoreSsl = false;
    }
    
    public SmsQueueItem(Context context, String id) {
        this.context = context;
        this.id = id;
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public String getRecipient() { return recipient; }
    public void setRecipient(String recipient) { this.recipient = recipient; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public long getTimestamp() { return timestamp; }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    
    public int getRetryCount() { return retryCount; }
    public void setRetryCount(int retryCount) { this.retryCount = retryCount; }
    
    public String getWebhookUrl() { return webhookUrl; }
    public void setWebhookUrl(String webhookUrl) { this.webhookUrl = webhookUrl; }
    
    public String getWebhookHeaders() { return webhookHeaders; }
    public void setWebhookHeaders(String webhookHeaders) { this.webhookHeaders = webhookHeaders; }
    
    public boolean isWebhookIgnoreSsl() { return webhookIgnoreSsl; }
    public void setWebhookIgnoreSsl(boolean webhookIgnoreSsl) { this.webhookIgnoreSsl = webhookIgnoreSsl; }
    
    public String getLogId() { return logId; }
    public void setLogId(String logId) { this.logId = logId; }
    
    public String getSender() { return sender; }
    public void setSender(String sender) { this.sender = sender; }
    
    public long getSentTime() { return sentTime; }
    public void setSentTime(long sentTime) { this.sentTime = sentTime; }
    
    public long getReceivedTime() { return receivedTime; }
    public void setReceivedTime(long receivedTime) { this.receivedTime = receivedTime; }
    
    public String getSimInfo() { return simInfo; }
    public void setSimInfo(String simInfo) { this.simInfo = simInfo; }
    
    /**
     * Get formatted timestamp for display
     */
    public String getFormattedTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }
    
    /**
     * Save this queue item to SharedPreferences
     */
    public void save() {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(KEY_TYPE, type != null ? type : "");
            jsonObject.put(KEY_RECIPIENT, recipient != null ? recipient : "");
            jsonObject.put(KEY_MESSAGE, message != null ? message : "");
            jsonObject.put(KEY_STATUS, status);
            jsonObject.put(KEY_TIMESTAMP, timestamp);
            jsonObject.put(KEY_RETRY_COUNT, retryCount);
            jsonObject.put(KEY_WEBHOOK_URL, webhookUrl != null ? webhookUrl : "");
            jsonObject.put(KEY_WEBHOOK_HEADERS, webhookHeaders != null ? webhookHeaders : "");
            jsonObject.put(KEY_WEBHOOK_IGNORE_SSL, webhookIgnoreSsl);
            jsonObject.put(KEY_LOG_ID, logId != null ? logId : "");
            jsonObject.put(KEY_SENDER, sender != null ? sender : "");
            jsonObject.put(KEY_SENT_TIME, sentTime);
            jsonObject.put(KEY_RECEIVED_TIME, receivedTime);
            jsonObject.put(KEY_SIM_INFO, simInfo != null ? simInfo : "");
            
            SharedPreferences.Editor editor = getEditor(context);
            editor.putString(id, jsonObject.toString());
            editor.apply();
        } catch (JSONException e) {
            Log.e("SmsQueueItem", "Error saving queue item: " + e.getMessage());
        }
    }
    
    /**
     * Remove this queue item from SharedPreferences
     */
    public void remove() {
        SharedPreferences.Editor editor = getEditor(context);
        editor.remove(id);
        editor.apply();
    }
    
    /**
     * Get all queue items from SharedPreferences
     */
    public static ArrayList<SmsQueueItem> getAll(Context context) {
        Map<String, ?> all = getPreference(context).getAll();
        ArrayList<SmsQueueItem> items = new ArrayList<>();
        
        for (Map.Entry<String, ?> entry : all.entrySet()) {
            if (entry.getValue() instanceof String) {
                try {
                    JSONObject jsonObject = new JSONObject((String) entry.getValue());
                    SmsQueueItem item = new SmsQueueItem(context, entry.getKey());
                    
                    item.setType(jsonObject.optString(KEY_TYPE, ""));
                    item.setRecipient(jsonObject.optString(KEY_RECIPIENT, ""));
                    item.setMessage(jsonObject.optString(KEY_MESSAGE, ""));
                    item.setStatus(jsonObject.optString(KEY_STATUS, STATUS_PENDING));
                    item.setTimestamp(jsonObject.optLong(KEY_TIMESTAMP, System.currentTimeMillis()));
                    item.setRetryCount(jsonObject.optInt(KEY_RETRY_COUNT, 0));
                    item.setWebhookUrl(jsonObject.optString(KEY_WEBHOOK_URL, ""));
                    item.setWebhookHeaders(jsonObject.optString(KEY_WEBHOOK_HEADERS, ""));
                    item.setWebhookIgnoreSsl(jsonObject.optBoolean(KEY_WEBHOOK_IGNORE_SSL, false));
                    item.setLogId(jsonObject.optString(KEY_LOG_ID, ""));
                    item.setSender(jsonObject.optString(KEY_SENDER, ""));
                    item.setSentTime(jsonObject.optLong(KEY_SENT_TIME, 0));
                    item.setReceivedTime(jsonObject.optLong(KEY_RECEIVED_TIME, 0));
                    item.setSimInfo(jsonObject.optString(KEY_SIM_INFO, ""));
                    
                    items.add(item);
                } catch (JSONException e) {
                    Log.e("SmsQueueItem", "Error parsing queue item: " + e.getMessage());
                }
            }
        }
        
        // Sort by timestamp (newest first)
        items.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
        
        return items;
    }
    
    /**
     * Clear all queue items (for testing or cleanup)
     */
    public static void clearAll(Context context) {
        SharedPreferences.Editor editor = getEditor(context);
        editor.clear();
        editor.apply();
    }
    
    private static SharedPreferences getPreference(Context context) {
        return context.getSharedPreferences("sms_queue", Context.MODE_PRIVATE);
    }
    
    private static SharedPreferences.Editor getEditor(Context context) {
        return getPreference(context).edit();
    }
}
