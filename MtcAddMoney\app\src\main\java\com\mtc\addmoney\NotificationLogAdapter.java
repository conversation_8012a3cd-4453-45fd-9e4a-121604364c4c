package com.mtc.addmoney;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;

/**
 * RecyclerView adapter for displaying notification logs
 */
public class NotificationLogAdapter extends RecyclerView.Adapter<NotificationLogAdapter.ViewHolder> {

    private ArrayList<NotificationLog> logs;

    public NotificationLogAdapter(ArrayList<NotificationLog> logs) {
        this.logs = logs;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_notification_log, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        NotificationLog log = logs.get(position);
        holder.bind(log);
    }

    @Override
    public int getItemCount() {
        return logs.size();
    }

    public void updateLogs(ArrayList<NotificationLog> newLogs) {
        this.logs = newLogs;
        notifyDataSetChanged();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView statusText;
        private final TextView timestampText;
        private final TextView senderText;
        private final TextView webhookUrlText;
        private final TextView responseCodeText;
        private final TextView responseMessageText;
        private final TextView attemptCountText;
        private final TextView messageContentText;
        private final LinearLayout responseLayout;
        private final LinearLayout attemptsLayout;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            statusText = itemView.findViewById(R.id.text_status);
            timestampText = itemView.findViewById(R.id.text_timestamp);
            senderText = itemView.findViewById(R.id.text_sender);
            webhookUrlText = itemView.findViewById(R.id.text_webhook_url);
            responseCodeText = itemView.findViewById(R.id.text_response_code);
            responseMessageText = itemView.findViewById(R.id.text_response_message);
            attemptCountText = itemView.findViewById(R.id.text_attempt_count);
            messageContentText = itemView.findViewById(R.id.text_message_content);
            responseLayout = itemView.findViewById(R.id.layout_response);
            attemptsLayout = itemView.findViewById(R.id.layout_attempts);
        }

        public void bind(NotificationLog log) {
            // Set status with appropriate styling
            statusText.setText(log.getStatusDisplay());
            setStatusBackground(log.getStatus());
            
            // Set basic info
            timestampText.setText(log.getFormattedTimestamp());
            senderText.setText(log.getSender() != null ? log.getSender() : "Unknown");
            webhookUrlText.setText(log.getWebhookUrl() != null ? log.getWebhookUrl() : "");
            messageContentText.setText(log.getMessageContent() != null ? log.getMessageContent() : "");

            // Show/hide response info based on status
            if (log.getStatus().equals(NotificationLog.STATUS_SUCCESS) || 
                log.getStatus().equals(NotificationLog.STATUS_FAILED)) {
                responseLayout.setVisibility(View.VISIBLE);
                responseCodeText.setText(String.valueOf(log.getResponseCode()));
                responseMessageText.setText(log.getResponseMessage() != null ? log.getResponseMessage() : "");
                
                // Color code response based on success/failure
                if (log.getStatus().equals(NotificationLog.STATUS_SUCCESS)) {
                    responseCodeText.setTextColor(itemView.getContext().getColor(android.R.color.holo_green_dark));
                } else {
                    responseCodeText.setTextColor(itemView.getContext().getColor(android.R.color.holo_red_dark));
                }
            } else {
                responseLayout.setVisibility(View.GONE);
            }

            // Show attempt count for retries or multiple attempts
            if (log.getAttemptCount() > 1 || log.getStatus().equals(NotificationLog.STATUS_RETRY)) {
                attemptsLayout.setVisibility(View.VISIBLE);
                attemptCountText.setText(String.valueOf(log.getAttemptCount()));
            } else {
                attemptsLayout.setVisibility(View.GONE);
            }

            // Make message content expandable on click
            messageContentText.setOnClickListener(v -> {
                if (messageContentText.getMaxLines() == 3) {
                    messageContentText.setMaxLines(Integer.MAX_VALUE);
                } else {
                    messageContentText.setMaxLines(3);
                }
            });
        }

        private void setStatusBackground(String status) {
            int backgroundResource;
            switch (status) {
                case NotificationLog.STATUS_SUCCESS:
                    backgroundResource = R.drawable.status_badge_success;
                    break;
                case NotificationLog.STATUS_FAILED:
                    backgroundResource = R.drawable.status_badge_failed;
                    break;
                case NotificationLog.STATUS_PENDING:
                    backgroundResource = R.drawable.status_badge_pending;
                    break;
                case NotificationLog.STATUS_RETRY:
                    backgroundResource = R.drawable.status_badge_retry;
                    break;
                default:
                    backgroundResource = R.drawable.status_badge_pending;
                    break;
            }
            statusText.setBackgroundResource(backgroundResource);
        }
    }
}
