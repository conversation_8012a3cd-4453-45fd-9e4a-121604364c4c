package com.mtc.addmoney;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.work.BackoffPolicy;
import androidx.work.Constraints;
import androidx.work.Data;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Receiver that monitors connectivity changes and processes queued items when connection is restored
 */
public class ConnectivityChangeReceiver extends BroadcastReceiver {
    
    private static final String TAG = "ConnectivityChangeReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Connectivity change detected");
        
        ConnectivityHelper connectivityHelper = new ConnectivityHelper(context);
        connectivityHelper.logConnectivityStatus();
        
        if (connectivityHelper.isConnected()) {
            Log.i(TAG, "Connection restored - processing queued items");
            
            // Start the background service to ensure it's running
            startBackgroundService(context);
            
            // Process queued webhooks
            processQueuedWebhooks(context);
            
            // Process queued SMS (though SMS doesn't require internet, 
            // we process them here for consistency)
            processQueuedSms(context);
        } else {
            Log.i(TAG, "Connection lost - items will be queued");
        }
    }
    
    private void startBackgroundService(Context context) {
        Intent serviceIntent = new Intent(context, BackgroundSmsService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent);
        } else {
            context.startService(serviceIntent);
        }
    }
    
    private void processQueuedWebhooks(Context context) {
        SmsQueueManager queueManager = new SmsQueueManager(context);
        List<SmsQueueItem> pendingWebhooks = queueManager.getPendingWebhooks();
        
        Log.d(TAG, "Processing " + pendingWebhooks.size() + " queued webhooks");
        
        for (SmsQueueItem webhook : pendingWebhooks) {
            scheduleWebhookWork(context, webhook);
        }
    }
    
    private void scheduleWebhookWork(Context context, SmsQueueItem webhook) {
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();

        Data data = new Data.Builder()
                .putString(WebHookWorkRequest.DATA_URL, webhook.getWebhookUrl())
                .putString(WebHookWorkRequest.DATA_TEXT, webhook.getMessage())
                .putString(WebHookWorkRequest.DATA_HEADERS, webhook.getWebhookHeaders())
                .putBoolean(WebHookWorkRequest.DATA_IGNORE_SSL, webhook.isWebhookIgnoreSsl())
                .putString(WebHookWorkRequest.DATA_LOG_ID, webhook.getLogId())
                .putString(WebHookWorkRequest.DATA_SENDER, webhook.getSender())
                .putString("QUEUE_ITEM_ID", webhook.getId()) // Add queue item ID for tracking
                .build();

        OneTimeWorkRequest request = new OneTimeWorkRequest.Builder(WebHookWorkRequest.class)
                .setConstraints(constraints)
                .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, OneTimeWorkRequest.MIN_BACKOFF_MILLIS, TimeUnit.MILLISECONDS)
                .setInputData(data)
                .addTag("queued_webhook_" + webhook.getId())
                .build();

        WorkManager.getInstance(context).enqueue(request);
        
        Log.d(TAG, "Scheduled webhook work for: " + webhook.getWebhookUrl());
    }
    
    private void processQueuedSms(Context context) {
        // SMS doesn't require internet connection, but we can process them here
        // to ensure they are sent when the device is active
        SmsQueueManager queueManager = new SmsQueueManager(context);
        List<SmsQueueItem> pendingSms = queueManager.getPendingSms();
        
        if (!pendingSms.isEmpty()) {
            Log.d(TAG, "Processing " + pendingSms.size() + " queued SMS");
            
            // Schedule SMS processing work
            scheduleQueuedSmsWork(context);
        }
    }
    
    private void scheduleQueuedSmsWork(Context context) {
        // Create a work request to process queued SMS
        Data data = new Data.Builder()
                .putString("WORK_TYPE", "PROCESS_SMS_QUEUE")
                .build();

        OneTimeWorkRequest request = new OneTimeWorkRequest.Builder(QueueProcessorWorkRequest.class)
                .setInputData(data)
                .addTag("process_sms_queue")
                .build();

        WorkManager.getInstance(context).enqueue(request);
        
        Log.d(TAG, "Scheduled SMS queue processing work");
    }
}
