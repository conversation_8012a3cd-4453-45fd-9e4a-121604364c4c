<?php
/**
 * Security Utility Class
 * 
 * Provides security functions for the webhook API
 */
class Security {
    
    /**
     * Validate and sanitize input data
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        
        if (is_string($data)) {
            // Remove null bytes
            $data = str_replace("\0", '', $data);
            
            // Trim whitespace
            $data = trim($data);
            
            // Convert special characters to HTML entities
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        
        return $data;
    }
    
    /**
     * Validate IP address
     */
    public static function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * Check if IP is in whitelist
     */
    public static function isIPWhitelisted($ip, $whitelist = []) {
        if (empty($whitelist)) {
            return true; // No whitelist means all IPs allowed
        }
        
        foreach ($whitelist as $allowedIP) {
            if (self::matchIPPattern($ip, $allowedIP)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if IP is in blacklist
     */
    public static function isIPBlacklisted($ip, $blacklist = []) {
        foreach ($blacklist as $blockedIP) {
            if (self::matchIPPattern($ip, $blockedIP)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Match IP against pattern (supports CIDR notation)
     */
    private static function matchIPPattern($ip, $pattern) {
        if ($ip === $pattern) {
            return true;
        }
        
        // Check CIDR notation
        if (strpos($pattern, '/') !== false) {
            list($subnet, $mask) = explode('/', $pattern);
            
            if (filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                return self::matchIPv4CIDR($ip, $subnet, $mask);
            } elseif (filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                return self::matchIPv6CIDR($ip, $subnet, $mask);
            }
        }
        
        return false;
    }
    
    /**
     * Match IPv4 CIDR
     */
    private static function matchIPv4CIDR($ip, $subnet, $mask) {
        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - $mask);
        
        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }
    
    /**
     * Match IPv6 CIDR
     */
    private static function matchIPv6CIDR($ip, $subnet, $mask) {
        $ipBin = inet_pton($ip);
        $subnetBin = inet_pton($subnet);
        
        if ($ipBin === false || $subnetBin === false) {
            return false;
        }
        
        $bytesToCheck = intval($mask / 8);
        $bitsToCheck = $mask % 8;
        
        // Check full bytes
        if ($bytesToCheck > 0 && substr($ipBin, 0, $bytesToCheck) !== substr($subnetBin, 0, $bytesToCheck)) {
            return false;
        }
        
        // Check remaining bits
        if ($bitsToCheck > 0) {
            $ipByte = ord($ipBin[$bytesToCheck]);
            $subnetByte = ord($subnetBin[$bytesToCheck]);
            $maskByte = 0xFF << (8 - $bitsToCheck);
            
            return ($ipByte & $maskByte) === ($subnetByte & $maskByte);
        }
        
        return true;
    }
    
    /**
     * Generate secure random string
     */
    public static function generateRandomString($length = 32) {
        if (function_exists('random_bytes')) {
            return bin2hex(random_bytes($length / 2));
        } elseif (function_exists('openssl_random_pseudo_bytes')) {
            return bin2hex(openssl_random_pseudo_bytes($length / 2));
        } else {
            // Fallback (less secure)
            $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $string = '';
            for ($i = 0; $i < $length; $i++) {
                $string .= $characters[rand(0, strlen($characters) - 1)];
            }
            return $string;
        }
    }
    
    /**
     * Hash password securely
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password hash
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate API key
     */
    public static function generateApiKey($prefix = 'wh_') {
        return $prefix . self::generateRandomString(40);
    }
    
    /**
     * Validate request signature (HMAC)
     */
    public static function validateSignature($payload, $signature, $secret) {
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Check for suspicious patterns in input
     */
    public static function detectSuspiciousPatterns($input) {
        $patterns = [
            // SQL injection patterns
            '/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i',
            '/(\b(OR|AND)\s+\d+\s*=\s*\d+)/i',
            '/(\'\s*(OR|AND)\s*\')/i',
            
            // XSS patterns
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            
            // Path traversal
            '/\.\.[\/\\\\]/i',
            
            // Command injection
            '/[;&|`$()]/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Log security event
     */
    public static function logSecurityEvent($event, $details = []) {
        $logEntry = [
            'timestamp' => date('c'),
            'event' => $event,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'details' => $details
        ];
        
        $logFile = __DIR__ . '/../storage/security.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Check request headers for security
     */
    public static function validateRequestHeaders() {
        $headers = getallheaders() ?: [];
        $issues = [];
        
        // Check for suspicious user agents
        $userAgent = $headers['User-Agent'] ?? '';
        $suspiciousAgents = ['sqlmap', 'nikto', 'nmap', 'masscan', 'zap'];
        
        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                $issues[] = 'Suspicious user agent detected';
                break;
            }
        }
        
        // Check for unusual content types
        $contentType = $headers['Content-Type'] ?? '';
        if (!empty($contentType) && !in_array($contentType, [
            'application/json',
            'application/x-www-form-urlencoded',
            'multipart/form-data',
            'text/plain'
        ])) {
            $issues[] = 'Unusual content type: ' . $contentType;
        }
        
        // Check for suspicious headers
        $suspiciousHeaders = ['X-Forwarded-For', 'X-Real-IP', 'X-Originating-IP'];
        foreach ($suspiciousHeaders as $header) {
            if (isset($headers[$header])) {
                $value = $headers[$header];
                if (self::detectSuspiciousPatterns($value)) {
                    $issues[] = "Suspicious value in $header header";
                }
            }
        }
        
        return $issues;
    }
    
    /**
     * Rate limiting with exponential backoff
     */
    public static function checkRateLimit($identifier, $maxRequests, $windowSeconds, $backoffMultiplier = 2) {
        $storageFile = __DIR__ . '/../storage/rate_limits_advanced.json';
        $limits = [];
        
        if (file_exists($storageFile)) {
            $limits = json_decode(file_get_contents($storageFile), true) ?: [];
        }
        
        $now = time();
        $windowStart = $now - $windowSeconds;
        
        // Clean old entries
        foreach ($limits as $id => &$data) {
            $data['requests'] = array_filter($data['requests'], function($timestamp) use ($windowStart) {
                return $timestamp > $windowStart;
            });
            
            if (empty($data['requests']) && (!isset($data['blocked_until']) || $data['blocked_until'] < $now)) {
                unset($limits[$id]);
            }
        }
        
        // Check if currently blocked
        if (isset($limits[$identifier]['blocked_until']) && $limits[$identifier]['blocked_until'] > $now) {
            return false;
        }
        
        // Initialize if not exists
        if (!isset($limits[$identifier])) {
            $limits[$identifier] = ['requests' => [], 'violations' => 0];
        }
        
        $requestCount = count($limits[$identifier]['requests']);
        
        if ($requestCount >= $maxRequests) {
            // Rate limit exceeded
            $limits[$identifier]['violations']++;
            $blockDuration = $windowSeconds * pow($backoffMultiplier, $limits[$identifier]['violations'] - 1);
            $limits[$identifier]['blocked_until'] = $now + $blockDuration;
            
            file_put_contents($storageFile, json_encode($limits));
            return false;
        }
        
        // Add current request
        $limits[$identifier]['requests'][] = $now;
        file_put_contents($storageFile, json_encode($limits));
        
        return true;
    }
}

/**
 * CSRF Protection Class
 */
class CSRFProtection {
    private static $tokenName = 'csrf_token';
    
    /**
     * Generate CSRF token
     */
    public static function generateToken() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = Security::generateRandomString(32);
        $_SESSION[self::$tokenName] = $token;
        
        return $token;
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateToken($token) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION[self::$tokenName])) {
            return false;
        }
        
        return hash_equals($_SESSION[self::$tokenName], $token);
    }
    
    /**
     * Get token for forms
     */
    public static function getTokenField() {
        $token = self::generateToken();
        return '<input type="hidden" name="' . self::$tokenName . '" value="' . $token . '">';
    }
}
?>
