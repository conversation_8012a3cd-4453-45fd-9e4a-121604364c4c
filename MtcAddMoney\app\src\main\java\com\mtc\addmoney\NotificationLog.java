package com.mtc.addmoney;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

/**
 * NotificationLog class to track webhook notification attempts and their results
 */
public class NotificationLog {
    
    // Status constants
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAILED = "failed";
    public static final String STATUS_RETRY = "retry";
    
    // JSON keys for storage
    private static final String KEY_WEBHOOK_URL = "webhook_url";
    private static final String KEY_MESSAGE_CONTENT = "message_content";
    private static final String KEY_STATUS = "status";
    private static final String KEY_TIMESTAMP = "timestamp";
    private static final String KEY_RESPONSE_CODE = "response_code";
    private static final String KEY_RESPONSE_MESSAGE = "response_message";
    private static final String KEY_ATTEMPT_COUNT = "attempt_count";
    private static final String KEY_SENDER = "sender";
    
    private final Context context;
    private String id;
    private String webhookUrl;
    private String messageContent;
    private String status;
    private long timestamp;
    private int responseCode;
    private String responseMessage;
    private int attemptCount;
    private String sender;
    
    public NotificationLog(Context context) {
        this.context = context;
        this.id = UUID.randomUUID().toString();
        this.timestamp = System.currentTimeMillis();
        this.status = STATUS_PENDING;
        this.attemptCount = 0;
        this.responseCode = 0;
        this.responseMessage = "";
    }
    
    public NotificationLog(Context context, String id) {
        this.context = context;
        this.id = id;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getWebhookUrl() {
        return webhookUrl;
    }
    
    public void setWebhookUrl(String webhookUrl) {
        this.webhookUrl = webhookUrl;
    }
    
    public String getMessageContent() {
        return messageContent;
    }
    
    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public int getResponseCode() {
        return responseCode;
    }
    
    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }
    
    public String getResponseMessage() {
        return responseMessage;
    }
    
    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }
    
    public int getAttemptCount() {
        return attemptCount;
    }
    
    public void setAttemptCount(int attemptCount) {
        this.attemptCount = attemptCount;
    }
    
    public String getSender() {
        return sender;
    }
    
    public void setSender(String sender) {
        this.sender = sender;
    }
    
    /**
     * Get formatted timestamp as readable string
     */
    public String getFormattedTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }
    
    /**
     * Get status display text with proper formatting
     */
    public String getStatusDisplay() {
        switch (status) {
            case STATUS_PENDING:
                return "Pending";
            case STATUS_SUCCESS:
                return "Success";
            case STATUS_FAILED:
                return "Failed";
            case STATUS_RETRY:
                return "Retrying";
            default:
                return "Unknown";
        }
    }
    
    /**
     * Save this notification log to SharedPreferences
     */
    public void save() {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(KEY_WEBHOOK_URL, webhookUrl != null ? webhookUrl : "");
            jsonObject.put(KEY_MESSAGE_CONTENT, messageContent != null ? messageContent : "");
            jsonObject.put(KEY_STATUS, status);
            jsonObject.put(KEY_TIMESTAMP, timestamp);
            jsonObject.put(KEY_RESPONSE_CODE, responseCode);
            jsonObject.put(KEY_RESPONSE_MESSAGE, responseMessage != null ? responseMessage : "");
            jsonObject.put(KEY_ATTEMPT_COUNT, attemptCount);
            jsonObject.put(KEY_SENDER, sender != null ? sender : "");
            
            SharedPreferences.Editor editor = getEditor(context);
            editor.putString(id, jsonObject.toString());
            editor.apply();
        } catch (JSONException e) {
            Log.e("NotificationLog", "Error saving notification log: " + e.getMessage());
        }
    }
    
    /**
     * Remove this notification log from SharedPreferences
     */
    public void remove() {
        SharedPreferences.Editor editor = getEditor(context);
        editor.remove(id);
        editor.apply();
    }
    
    /**
     * Get all notification logs from SharedPreferences
     */
    public static ArrayList<NotificationLog> getAll(Context context) {
        Map<String, ?> all = getPreference(context).getAll();
        ArrayList<NotificationLog> logs = new ArrayList<>();
        
        for (Map.Entry<String, ?> entry : all.entrySet()) {
            String value = (String) entry.getValue();
            if (value != null && value.startsWith("{")) {
                try {
                    JSONObject jsonObject = new JSONObject(value);
                    NotificationLog log = new NotificationLog(context, entry.getKey());
                    
                    log.setWebhookUrl(jsonObject.optString(KEY_WEBHOOK_URL, ""));
                    log.setMessageContent(jsonObject.optString(KEY_MESSAGE_CONTENT, ""));
                    log.setStatus(jsonObject.optString(KEY_STATUS, STATUS_PENDING));
                    log.setTimestamp(jsonObject.optLong(KEY_TIMESTAMP, System.currentTimeMillis()));
                    log.setResponseCode(jsonObject.optInt(KEY_RESPONSE_CODE, 0));
                    log.setResponseMessage(jsonObject.optString(KEY_RESPONSE_MESSAGE, ""));
                    log.setAttemptCount(jsonObject.optInt(KEY_ATTEMPT_COUNT, 0));
                    log.setSender(jsonObject.optString(KEY_SENDER, ""));
                    
                    logs.add(log);
                } catch (JSONException e) {
                    Log.e("NotificationLog", "Error parsing notification log: " + e.getMessage());
                }
            }
        }
        
        // Sort by timestamp (newest first)
        logs.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
        
        return logs;
    }
    
    /**
     * Clear all notification logs (for testing or cleanup)
     */
    public static void clearAll(Context context) {
        SharedPreferences.Editor editor = getEditor(context);
        editor.clear();
        editor.apply();
    }
    
    private static SharedPreferences getPreference(Context context) {
        return context.getSharedPreferences("notification_logs", Context.MODE_PRIVATE);
    }
    
    private static SharedPreferences.Editor getEditor(Context context) {
        return getPreference(context).edit();
    }
}
