package com.mtc.addmoney;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;

/**
 * Helper class for handling battery optimization and doze mode
 */
public class BatteryOptimizationHelper {
    
    private static final String TAG = "BatteryOptimizationHelper";
    
    /**
     * Check if the app is whitelisted from battery optimization
     */
    public static boolean isIgnoringBatteryOptimizations(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (powerManager != null) {
                return powerManager.isIgnoringBatteryOptimizations(context.getPackageName());
            }
        }
        return true; // Assume true for older versions
    }
    
    /**
     * Request to ignore battery optimizations
     */
    @SuppressLint("BatteryLife")
    public static void requestIgnoreBatteryOptimizations(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!isIgnoringBatteryOptimizations(context)) {
                try {
                    Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                    intent.setData(Uri.parse("package:" + context.getPackageName()));
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(intent);
                    Log.d(TAG, "Requested battery optimization whitelist");
                } catch (Exception e) {
                    Log.e(TAG, "Failed to request battery optimization whitelist", e);
                    // Fallback to general battery optimization settings
                    openBatteryOptimizationSettings(context);
                }
            }
        }
    }
    
    /**
     * Open battery optimization settings page
     */
    public static void openBatteryOptimizationSettings(Context context) {
        try {
            Intent intent = new Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            Log.d(TAG, "Opened battery optimization settings");
        } catch (Exception e) {
            Log.e(TAG, "Failed to open battery optimization settings", e);
        }
    }
    
    /**
     * Show dialog explaining battery optimization and requesting whitelist
     */
    public static void showBatteryOptimizationDialog(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !isIgnoringBatteryOptimizations(context)) {
            new AlertDialog.Builder(context)
                    .setTitle("Battery Optimization")
                    .setMessage("To ensure SMS functionality works reliably when the app is closed, " +
                               "please disable battery optimization for this app.\n\n" +
                               "This will allow the app to receive and process SMS messages even when " +
                               "the device is in doze mode or the app is in the background.")
                    .setPositiveButton("Disable Optimization", (dialog, which) -> {
                        requestIgnoreBatteryOptimizations(context);
                    })
                    .setNegativeButton("Settings", (dialog, which) -> {
                        openBatteryOptimizationSettings(context);
                    })
                    .setNeutralButton("Later", null)
                    .setCancelable(true)
                    .show();
        }
    }
    
    /**
     * Check if device is in doze mode
     */
    public static boolean isDeviceIdleMode(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (powerManager != null) {
                return powerManager.isDeviceIdleMode();
            }
        }
        return false;
    }
    
    /**
     * Check if app is in standby mode
     */
    public static boolean isAppStandby(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (powerManager != null) {
                return powerManager.isDeviceIdleMode(); // This covers app standby as well
            }
        }
        return false;
    }
    
    /**
     * Log current power management status
     */
    public static void logPowerManagementStatus(Context context) {
        boolean isIgnoringOptimizations = isIgnoringBatteryOptimizations(context);
        boolean isDeviceIdle = isDeviceIdleMode(context);
        
        Log.i(TAG, "Power Management Status:");
        Log.i(TAG, "  - Ignoring battery optimizations: " + isIgnoringOptimizations);
        Log.i(TAG, "  - Device in doze mode: " + isDeviceIdle);
        Log.i(TAG, "  - Android version: " + Build.VERSION.SDK_INT);
    }
    
    /**
     * Get battery optimization status as string
     */
    public static String getBatteryOptimizationStatus(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (isIgnoringBatteryOptimizations(context)) {
                return "Disabled (Recommended)";
            } else {
                return "Enabled (May affect SMS functionality)";
            }
        } else {
            return "Not applicable (Android < 6.0)";
        }
    }
}
