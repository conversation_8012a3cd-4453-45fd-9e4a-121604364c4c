<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/green"
    tools:context=".MainActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="#2794FF"
        android:title="@string/app_name"
        android:titleTextColor="@android:color/white" />

    <!-- Info Text -->
    <TextView
        android:id="@+id/info_notice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="72dp"
        android:layout_marginEnd="16dp"
        android:gravity="center_horizontal"
        android:text="Welcome!"
        android:textAppearance="?android:attr/textAppearanceMedium" />

    <!-- List of Configs -->
    <ListView
        android:id="@+id/listView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@id/info_notice"
        android:layout_gravity="top"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="72dp" />

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/btn_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/add"
        android:src="@drawable/ic_btn_add"
        app:backgroundTint="#2196F3"
        app:tint="@android:color/white" />


</androidx.coordinatorlayout.widget.CoordinatorLayout>
