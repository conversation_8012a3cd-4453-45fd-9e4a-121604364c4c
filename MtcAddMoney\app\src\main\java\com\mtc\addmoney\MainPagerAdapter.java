package com.mtc.addmoney;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

/**
 * ViewPager2 adapter for managing main activity tabs
 */
public class MainPagerAdapter extends FragmentStateAdapter {

    private static final int TAB_COUNT = 2;
    public static final int TAB_WEBHOOKS = 0;
    public static final int TAB_MONITOR = 1;

    private WebhooksFragment webhooksFragment;
    private MonitorFragment monitorFragment;

    public MainPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case TAB_WEBHOOKS:
                if (webhooksFragment == null) {
                    webhooksFragment = new WebhooksFragment();
                }
                return webhooksFragment;
            case TAB_MONITOR:
                if (monitorFragment == null) {
                    monitorFragment = new MonitorFragment();
                }
                return monitorFragment;
            default:
                throw new IllegalArgumentException("Invalid tab position: " + position);
        }
    }

    /**
     * Force creation of the webhooks fragment if it doesn't exist
     */
    public void ensureWebhooksFragmentCreated() {
        if (webhooksFragment == null) {
            webhooksFragment = new WebhooksFragment();
        }
    }

    @Override
    public int getItemCount() {
        return TAB_COUNT;
    }

    /**
     * Get the webhooks fragment instance
     */
    public WebhooksFragment getWebhooksFragment() {
        return webhooksFragment;
    }

    /**
     * Get the monitor fragment instance
     */
    public MonitorFragment getMonitorFragment() {
        return monitorFragment;
    }
}
