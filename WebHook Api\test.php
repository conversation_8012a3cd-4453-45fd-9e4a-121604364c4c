<?php
/**
 * Simple PHP Test File
 * 
 * Basic test to ensure PHP is working correctly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>PHP Test</title></head><body>";
echo "<h1>PHP Test Page</h1>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";

// Test JSON
$testArray = array('test' => 'success', 'timestamp' => time());
echo "<p><strong>JSON Test:</strong> " . json_encode($testArray) . "</p>";

// Test file operations
$testFile = __DIR__ . '/test_write.txt';
if (file_put_contents($testFile, 'Test content')) {
    echo "<p><strong>File Write Test:</strong> ✓ Success</p>";
    unlink($testFile); // Clean up
} else {
    echo "<p><strong>File Write Test:</strong> ✗ Failed</p>";
}

echo "<p><a href='index.php'>← Back to Main Page</a></p>";
echo "</body></html>";
?>
