package com.mtc.addmoney;

import android.content.Context;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * Manager class for handling SMS and webhook queue operations
 */
public class SmsQueueManager {
    
    private static final String TAG = "SmsQueueManager";
    private final Context context;
    
    public SmsQueueManager(Context context) {
        this.context = context;
    }
    
    /**
     * Queue an SMS for sending when connectivity is available
     */
    public SmsQueueItem queueSms(String recipient, String message) {
        SmsQueueItem item = new SmsQueueItem(context);
        item.setType(SmsQueueItem.TYPE_SMS);
        item.setRecipient(recipient);
        item.setMessage(message);
        item.setStatus(SmsQueueItem.STATUS_PENDING);
        item.save();
        
        Log.d(TAG, "Queued SMS for recipient: " + recipient);
        return item;
    }
    
    /**
     * Queue a webhook for sending when connectivity is available
     */
    public SmsQueueItem queueWebhook(String url, String jsonPayload, String headers, 
                                   boolean ignoreSsl, String logId, String sender) {
        SmsQueueItem item = new SmsQueueItem(context);
        item.setType(SmsQueueItem.TYPE_WEBHOOK);
        item.setWebhookUrl(url);
        item.setMessage(jsonPayload);
        item.setWebhookHeaders(headers);
        item.setWebhookIgnoreSsl(ignoreSsl);
        item.setLogId(logId);
        item.setSender(sender);
        item.setStatus(SmsQueueItem.STATUS_PENDING);
        item.save();
        
        Log.d(TAG, "Queued webhook for URL: " + url);
        return item;
    }
    
    /**
     * Store a received SMS for tracking
     */
    public SmsQueueItem storeReceivedSms(String sender, String message, long sentTime, 
                                       long receivedTime, String simInfo) {
        SmsQueueItem item = new SmsQueueItem(context);
        item.setType(SmsQueueItem.TYPE_RECEIVED_SMS);
        item.setSender(sender);
        item.setMessage(message);
        item.setSentTime(sentTime);
        item.setReceivedTime(receivedTime);
        item.setSimInfo(simInfo);
        item.setStatus(SmsQueueItem.STATUS_SENT); // Received SMS is considered "sent" in terms of processing
        item.save();
        
        Log.d(TAG, "Stored received SMS from: " + sender);
        return item;
    }
    
    /**
     * Get all pending SMS items
     */
    public List<SmsQueueItem> getPendingSms() {
        List<SmsQueueItem> allItems = SmsQueueItem.getAll(context);
        List<SmsQueueItem> pendingSms = new ArrayList<>();
        
        for (SmsQueueItem item : allItems) {
            if (SmsQueueItem.TYPE_SMS.equals(item.getType()) && 
                SmsQueueItem.STATUS_PENDING.equals(item.getStatus())) {
                pendingSms.add(item);
            }
        }
        
        return pendingSms;
    }
    
    /**
     * Get all pending webhook items
     */
    public List<SmsQueueItem> getPendingWebhooks() {
        List<SmsQueueItem> allItems = SmsQueueItem.getAll(context);
        List<SmsQueueItem> pendingWebhooks = new ArrayList<>();
        
        for (SmsQueueItem item : allItems) {
            if (SmsQueueItem.TYPE_WEBHOOK.equals(item.getType()) && 
                SmsQueueItem.STATUS_PENDING.equals(item.getStatus())) {
                pendingWebhooks.add(item);
            }
        }
        
        return pendingWebhooks;
    }
    
    /**
     * Get all received SMS items
     */
    public List<SmsQueueItem> getReceivedSms() {
        List<SmsQueueItem> allItems = SmsQueueItem.getAll(context);
        List<SmsQueueItem> receivedSms = new ArrayList<>();
        
        for (SmsQueueItem item : allItems) {
            if (SmsQueueItem.TYPE_RECEIVED_SMS.equals(item.getType())) {
                receivedSms.add(item);
            }
        }
        
        return receivedSms;
    }
    
    /**
     * Mark an SMS as sent
     */
    public void markSmsSent(String itemId) {
        SmsQueueItem item = findById(itemId);
        if (item != null) {
            item.setStatus(SmsQueueItem.STATUS_SENT);
            item.save();
            Log.d(TAG, "Marked SMS as sent: " + itemId);
        }
    }
    
    /**
     * Mark an SMS as failed
     */
    public void markSmsFailed(String itemId) {
        SmsQueueItem item = findById(itemId);
        if (item != null) {
            item.setStatus(SmsQueueItem.STATUS_FAILED);
            item.setRetryCount(item.getRetryCount() + 1);
            item.save();
            Log.d(TAG, "Marked SMS as failed: " + itemId);
        }
    }
    
    /**
     * Mark a webhook as sent
     */
    public void markWebhookSent(String itemId) {
        SmsQueueItem item = findById(itemId);
        if (item != null) {
            item.setStatus(SmsQueueItem.STATUS_SENT);
            item.save();
            Log.d(TAG, "Marked webhook as sent: " + itemId);
        }
    }
    
    /**
     * Mark a webhook as failed
     */
    public void markWebhookFailed(String itemId) {
        SmsQueueItem item = findById(itemId);
        if (item != null) {
            item.setStatus(SmsQueueItem.STATUS_FAILED);
            item.setRetryCount(item.getRetryCount() + 1);
            item.save();
            Log.d(TAG, "Marked webhook as failed: " + itemId);
        }
    }
    
    /**
     * Retry failed items (reset status to pending)
     */
    public void retryFailedItems() {
        List<SmsQueueItem> allItems = SmsQueueItem.getAll(context);
        int retryCount = 0;
        
        for (SmsQueueItem item : allItems) {
            if (SmsQueueItem.STATUS_FAILED.equals(item.getStatus()) && item.getRetryCount() < 3) {
                item.setStatus(SmsQueueItem.STATUS_PENDING);
                item.save();
                retryCount++;
            }
        }
        
        Log.d(TAG, "Retried " + retryCount + " failed items");
    }
    
    /**
     * Clean up old items (older than specified days)
     */
    public void cleanupOldItems(int daysToKeep) {
        long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24L * 60L * 60L * 1000L);
        List<SmsQueueItem> allItems = SmsQueueItem.getAll(context);
        
        int deletedCount = 0;
        for (SmsQueueItem item : allItems) {
            if (item.getTimestamp() < cutoffTime && 
                (SmsQueueItem.STATUS_SENT.equals(item.getStatus()) || 
                 SmsQueueItem.STATUS_FAILED.equals(item.getStatus()))) {
                item.remove();
                deletedCount++;
            }
        }
        
        Log.d(TAG, "Cleaned up " + deletedCount + " old queue items");
    }
    
    /**
     * Get queue statistics
     */
    public QueueStats getStats() {
        List<SmsQueueItem> allItems = SmsQueueItem.getAll(context);
        QueueStats stats = new QueueStats();
        
        for (SmsQueueItem item : allItems) {
            stats.totalCount++;
            
            switch (item.getStatus()) {
                case SmsQueueItem.STATUS_PENDING:
                    stats.pendingCount++;
                    break;
                case SmsQueueItem.STATUS_SENT:
                    stats.sentCount++;
                    break;
                case SmsQueueItem.STATUS_FAILED:
                    stats.failedCount++;
                    break;
            }
            
            switch (item.getType()) {
                case SmsQueueItem.TYPE_SMS:
                    stats.smsCount++;
                    break;
                case SmsQueueItem.TYPE_WEBHOOK:
                    stats.webhookCount++;
                    break;
                case SmsQueueItem.TYPE_RECEIVED_SMS:
                    stats.receivedSmsCount++;
                    break;
            }
        }
        
        return stats;
    }
    
    /**
     * Find queue item by ID
     */
    private SmsQueueItem findById(String id) {
        List<SmsQueueItem> allItems = SmsQueueItem.getAll(context);
        for (SmsQueueItem item : allItems) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * Statistics class for queue items
     */
    public static class QueueStats {
        public int totalCount = 0;
        public int pendingCount = 0;
        public int sentCount = 0;
        public int failedCount = 0;
        public int smsCount = 0;
        public int webhookCount = 0;
        public int receivedSmsCount = 0;
    }
}
