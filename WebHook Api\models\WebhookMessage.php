<?php
require_once __DIR__ . '/../config/database.php';

/**
 * WebhookMessage Model
 * 
 * Handles all database operations related to webhook messages
 */
class WebhookMessage {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Save a new webhook message to the database
     */
    public function save($data) {
        $sql = "INSERT INTO webhook_messages (
            message_id, sender, message_content, message_type, 
            headers, raw_payload, ip_address, user_agent, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['message_id'] ?? $this->generateMessageId(),
            $data['sender'] ?? 'unknown',
            $data['message_content'] ?? '',
            $data['message_type'] ?? 'sms',
            json_encode($data['headers'] ?? []),
            json_encode($data['raw_payload'] ?? []),
            $data['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? null,
            $data['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? null,
            $data['status'] ?? 'received'
        ];
        
        return $this->db->insert($sql, $params);
    }
    
    /**
     * Get a message by ID
     */
    public function getById($id) {
        $sql = "SELECT * FROM webhook_messages WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }
    
    /**
     * Get a message by message_id
     */
    public function getByMessageId($messageId) {
        $sql = "SELECT * FROM webhook_messages WHERE message_id = ?";
        return $this->db->fetchOne($sql, [$messageId]);
    }
    
    /**
     * Get recent messages with pagination
     */
    public function getRecent($limit = 50, $offset = 0, $filters = []) {
        $sql = "SELECT * FROM webhook_messages WHERE 1=1";
        $params = [];
        
        // Apply filters
        if (!empty($filters['sender'])) {
            $sql .= " AND sender LIKE ?";
            $params[] = '%' . $filters['sender'] . '%';
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['message_type'])) {
            $sql .= " AND message_type = ?";
            $params[] = $filters['message_type'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND received_at >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND received_at <= ?";
            $params[] = $filters['date_to'];
        }
        
        $sql .= " ORDER BY received_at DESC LIMIT ? OFFSET ?";
        $params[] = (int)$limit;
        $params[] = (int)$offset;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get message count with filters
     */
    public function getCount($filters = []) {
        $sql = "SELECT COUNT(*) as count FROM webhook_messages WHERE 1=1";
        $params = [];
        
        // Apply same filters as getRecent
        if (!empty($filters['sender'])) {
            $sql .= " AND sender LIKE ?";
            $params[] = '%' . $filters['sender'] . '%';
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['message_type'])) {
            $sql .= " AND message_type = ?";
            $params[] = $filters['message_type'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND received_at >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND received_at <= ?";
            $params[] = $filters['date_to'];
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] ?? 0;
    }
    
    /**
     * Update message status
     */
    public function updateStatus($id, $status, $errorMessage = null) {
        $sql = "UPDATE webhook_messages SET status = ?, error_message = ?, processed_at = NOW() WHERE id = ?";
        return $this->db->update($sql, [$status, $errorMessage, $id]);
    }
    
    /**
     * Delete old messages (cleanup)
     */
    public function deleteOldMessages($daysOld = 30) {
        $sql = "DELETE FROM webhook_messages WHERE received_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        return $this->db->delete($sql, [$daysOld]);
    }
    
    /**
     * Get message statistics
     */
    public function getStats($days = 7) {
        $sql = "SELECT 
            DATE(received_at) as date,
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'processed' THEN 1 END) as processed,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
            COUNT(CASE WHEN status = 'received' THEN 1 END) as pending
        FROM webhook_messages 
        WHERE received_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(received_at)
        ORDER BY date DESC";
        
        return $this->db->fetchAll($sql, [$days]);
    }
    
    /**
     * Search messages by content
     */
    public function search($query, $limit = 50) {
        $sql = "SELECT * FROM webhook_messages 
                WHERE message_content LIKE ? OR sender LIKE ?
                ORDER BY received_at DESC 
                LIMIT ?";
        
        $searchTerm = '%' . $query . '%';
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $limit]);
    }
    
    /**
     * Generate a unique message ID
     */
    private function generateMessageId() {
        return uniqid('msg_', true) . '_' . time();
    }
    
    /**
     * Validate message data
     */
    public function validate($data) {
        $errors = [];
        
        if (empty($data['message_content'])) {
            $errors[] = 'Message content is required';
        }
        
        if (empty($data['sender'])) {
            $errors[] = 'Sender is required';
        }
        
        if (!empty($data['message_type']) && !in_array($data['message_type'], ['sms', 'email', 'push', 'other'])) {
            $errors[] = 'Invalid message type';
        }
        
        return $errors;
    }
}
?>
