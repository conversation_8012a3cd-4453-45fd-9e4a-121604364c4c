<?php
/**
 * Diagnostic Script
 * 
 * Checks system requirements and identifies issues
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook API Diagnostics</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .check { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .status { font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Webhook API System Diagnostics</h1>
        
        <h2>PHP Environment</h2>
        <?php
        echo '<div class="check info">';
        echo '<strong>PHP Version:</strong> ' . PHP_VERSION;
        if (version_compare(PHP_VERSION, '7.0.0', '>=')) {
            echo ' <span class="status" style="color: green;">✓ Compatible</span>';
        } else {
            echo ' <span class="status" style="color: red;">✗ Requires PHP 7.0+</span>';
        }
        echo '</div>';
        
        echo '<div class="check info">';
        echo '<strong>Server:</strong> ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown');
        echo '</div>';
        
        echo '<div class="check info">';
        echo '<strong>Document Root:</strong> ' . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown');
        echo '</div>';
        
        echo '<div class="check info">';
        echo '<strong>Current Directory:</strong> ' . __DIR__;
        echo '</div>';
        ?>
        
        <h2>Required PHP Extensions</h2>
        <?php
        $requiredExtensions = [
            'pdo' => 'PDO (Database connectivity)',
            'pdo_mysql' => 'PDO MySQL (MySQL database support)',
            'json' => 'JSON (JSON processing)',
            'curl' => 'cURL (HTTP requests)',
            'mbstring' => 'Multibyte String (String handling)',
            'openssl' => 'OpenSSL (Security functions)'
        ];
        
        foreach ($requiredExtensions as $ext => $description) {
            $loaded = extension_loaded($ext);
            echo '<div class="check ' . ($loaded ? 'pass' : 'fail') . '">';
            echo '<strong>' . $ext . '</strong>: ' . $description;
            echo ' <span class="status">' . ($loaded ? '✓ Loaded' : '✗ Missing') . '</span>';
            echo '</div>';
        }
        ?>
        
        <h2>File System Checks</h2>
        <?php
        $directories = [
            __DIR__ . '/storage' => 'Storage directory (for logs and cache)',
            __DIR__ . '/config' => 'Configuration directory',
            __DIR__ . '/api' => 'API directory',
            __DIR__ . '/models' => 'Models directory'
        ];
        
        foreach ($directories as $dir => $description) {
            $exists = is_dir($dir);
            $writable = $exists ? is_writable($dir) : false;
            
            echo '<div class="check ' . ($exists ? ($writable ? 'pass' : 'warning') : 'fail') . '">';
            echo '<strong>' . basename($dir) . '</strong>: ' . $description;
            
            if (!$exists) {
                echo ' <span class="status">✗ Does not exist</span>';
                // Try to create it
                if (mkdir($dir, 0755, true)) {
                    echo ' <span class="status" style="color: green;">✓ Created successfully</span>';
                } else {
                    echo ' <span class="status" style="color: red;">✗ Failed to create</span>';
                }
            } elseif (!$writable) {
                echo ' <span class="status">⚠ Not writable</span>';
            } else {
                echo ' <span class="status">✓ OK</span>';
            }
            echo '</div>';
        }
        ?>
        
        <h2>Database Connection Test</h2>
        <?php
        try {
            // Check if database config file exists
            $configFile = __DIR__ . '/config/database.php';
            if (!file_exists($configFile)) {
                echo '<div class="check fail">';
                echo '<strong>Database Config:</strong> Configuration file missing';
                echo ' <span class="status">✗ File not found: ' . $configFile . '</span>';
                echo '</div>';
            } else {
                echo '<div class="check pass">';
                echo '<strong>Database Config:</strong> Configuration file found';
                echo ' <span class="status">✓ OK</span>';
                echo '</div>';
                
                // Try to include and test database connection
                require_once $configFile;
                
                try {
                    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
                    $pdo = new PDO($dsn, DB_USER, DB_PASS, DB_OPTIONS);
                    
                    echo '<div class="check pass">';
                    echo '<strong>Database Connection:</strong> Successfully connected to MySQL server';
                    echo ' <span class="status">✓ Connected</span>';
                    echo '</div>';
                    
                    // Check if database exists
                    $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
                    if ($stmt->rowCount() > 0) {
                        echo '<div class="check pass">';
                        echo '<strong>Database Exists:</strong> Database "' . DB_NAME . '" found';
                        echo ' <span class="status">✓ Exists</span>';
                        echo '</div>';
                    } else {
                        echo '<div class="check warning">';
                        echo '<strong>Database Missing:</strong> Database "' . DB_NAME . '" not found';
                        echo ' <span class="status">⚠ Run setup.php to create</span>';
                        echo '</div>';
                    }
                    
                } catch (PDOException $e) {
                    echo '<div class="check fail">';
                    echo '<strong>Database Connection:</strong> Failed to connect';
                    echo ' <span class="status">✗ Error: ' . $e->getMessage() . '</span>';
                    echo '</div>';
                }
            }
        } catch (Exception $e) {
            echo '<div class="check fail">';
            echo '<strong>Database Test:</strong> Error during testing';
            echo ' <span class="status">✗ Error: ' . $e->getMessage() . '</span>';
            echo '</div>';
        }
        ?>
        
        <h2>File Permissions</h2>
        <?php
        $files = [
            __DIR__ . '/index.html' => 'Main dashboard',
            __DIR__ . '/setup.php' => 'Setup script',
            __DIR__ . '/api/webhook.php' => 'Webhook endpoint',
            __DIR__ . '/config/database.php' => 'Database config'
        ];
        
        foreach ($files as $file => $description) {
            $exists = file_exists($file);
            $readable = $exists ? is_readable($file) : false;
            
            echo '<div class="check ' . ($exists && $readable ? 'pass' : 'fail') . '">';
            echo '<strong>' . basename($file) . '</strong>: ' . $description;
            
            if (!$exists) {
                echo ' <span class="status">✗ File missing</span>';
            } elseif (!$readable) {
                echo ' <span class="status">✗ Not readable</span>';
            } else {
                echo ' <span class="status">✓ OK</span>';
            }
            echo '</div>';
        }
        ?>
        
        <h2>Error Log Check</h2>
        <?php
        $errorLog = ini_get('error_log');
        if ($errorLog) {
            echo '<div class="check info">';
            echo '<strong>PHP Error Log:</strong> ' . $errorLog;
            
            if (file_exists($errorLog) && is_readable($errorLog)) {
                $logContent = file_get_contents($errorLog);
                $recentErrors = array_slice(explode("\n", $logContent), -10);
                $recentErrors = array_filter($recentErrors);
                
                if (!empty($recentErrors)) {
                    echo '<br><strong>Recent Errors:</strong>';
                    echo '<pre>' . htmlspecialchars(implode("\n", $recentErrors)) . '</pre>';
                } else {
                    echo '<br><span class="status">✓ No recent errors</span>';
                }
            }
            echo '</div>';
        }
        ?>
        
        <h2>Quick Actions</h2>
        <div style="margin: 20px 0;">
            <a href="setup.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Run Setup</a>
            <a href="index.html" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;">Go to Dashboard</a>
            <a href="api/webhook.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Test Webhook</a>
        </div>
        
        <h2>System Information</h2>
        <div class="check info">
            <strong>PHP Configuration:</strong>
            <pre><?php
            $config = [
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'display_errors' => ini_get('display_errors') ? 'On' : 'Off',
                'log_errors' => ini_get('log_errors') ? 'On' : 'Off'
            ];
            
            foreach ($config as $key => $value) {
                echo $key . ': ' . $value . "\n";
            }
            ?></pre>
        </div>
    </div>
</body>
</html>
