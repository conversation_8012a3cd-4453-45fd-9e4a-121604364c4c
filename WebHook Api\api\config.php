<?php
/**
 * Configuration API Endpoint
 * 
 * Handles configuration management for the webhook system
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../models/WebhookConfig.php';
require_once __DIR__ . '/../utils/ApiResponse.php';

$config = new WebhookConfig();
$response = new ApiResponse();

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $path = $_GET['path'] ?? '';
    
    switch ($method) {
        case 'GET':
            handleGet($config, $response, $path);
            break;
            
        case 'POST':
        case 'PUT':
            handleUpdate($config, $response);
            break;
            
        case 'DELETE':
            handleDelete($config, $response, $path);
            break;
            
        default:
            $response->error('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Config API error: " . $e->getMessage());
    $response->error('Internal server error', 500);
}

/**
 * Handle GET requests
 */
function handleGet($config, $response, $path) {
    if ($path === 'all') {
        // Get all configuration settings
        $allConfig = $config->getAll();
        $response->success($allConfig, 'Configuration retrieved successfully');
    } elseif ($path === 'webhook-url') {
        // Get formatted webhook URL
        $url = $config->getWebhookUrl();
        $response->success(['webhook_url' => $url], 'Webhook URL retrieved successfully');
    } elseif (!empty($path)) {
        // Get specific configuration value
        $value = $config->get($path);
        if ($value !== null) {
            $response->success(['key' => $path, 'value' => $value], 'Configuration value retrieved');
        } else {
            $response->error('Configuration key not found', 404);
        }
    } else {
        // Get essential configuration for dashboard
        $essentialConfig = [
            'webhook_url' => $config->getWebhookUrl(),
            'max_message_size' => $config->getInt('max_message_size', 10485760),
            'rate_limit_per_minute' => $config->getInt('rate_limit_per_minute', 100),
            'require_api_key' => $config->getBool('require_api_key', false),
            'log_all_requests' => $config->getBool('log_all_requests', true),
            'auto_process_messages' => $config->getBool('auto_process_messages', true)
        ];
        
        $response->success($essentialConfig, 'Essential configuration retrieved');
    }
}

/**
 * Handle POST/PUT requests (update configuration)
 */
function handleUpdate($config, $response) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $response->error('Invalid JSON format', 400);
        return;
    }
    
    if (empty($input)) {
        $response->error('No configuration data provided', 400);
        return;
    }
    
    $errors = [];
    $updated = [];
    
    foreach ($input as $key => $value) {
        // Validate configuration value
        $validationErrors = $config->validate($key, $value);
        if (!empty($validationErrors)) {
            $errors[$key] = $validationErrors;
            continue;
        }
        
        try {
            $config->set($key, $value);
            $updated[$key] = $value;
        } catch (Exception $e) {
            $errors[$key] = ['Failed to update: ' . $e->getMessage()];
        }
    }
    
    if (!empty($errors)) {
        $response->error('Some configurations failed to update', 422, [
            'updated' => $updated,
            'errors' => $errors
        ]);
        return;
    }
    
    $response->success($updated, 'Configuration updated successfully');
}

/**
 * Handle DELETE requests
 */
function handleDelete($config, $response, $path) {
    if (empty($path)) {
        $response->error('Configuration key is required', 400);
        return;
    }
    
    $deleted = $config->delete($path);
    
    if ($deleted > 0) {
        $response->success(['deleted_key' => $path], 'Configuration deleted successfully');
    } else {
        $response->error('Configuration key not found', 404);
    }
}
?>
