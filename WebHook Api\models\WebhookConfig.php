<?php
require_once __DIR__ . '/../config/database.php';

/**
 * WebhookConfig Model
 * 
 * Handles configuration settings for the webhook system
 */
class WebhookConfig {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get a configuration value
     */
    public function get($key, $default = null) {
        $sql = "SELECT config_value FROM webhook_config WHERE config_key = ?";
        $result = $this->db->fetchOne($sql, [$key]);
        
        if ($result) {
            return $result['config_value'];
        }
        
        return $default;
    }
    
    /**
     * Set a configuration value
     */
    public function set($key, $value, $description = null) {
        $sql = "INSERT INTO webhook_config (config_key, config_value, description) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                config_value = VALUES(config_value),
                description = COALESCE(VALUES(description), description),
                updated_at = CURRENT_TIMESTAMP";
        
        return $this->db->execute($sql, [$key, $value, $description]);
    }
    
    /**
     * Get all configuration settings
     */
    public function getAll() {
        $sql = "SELECT * FROM webhook_config ORDER BY config_key";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Delete a configuration setting
     */
    public function delete($key) {
        $sql = "DELETE FROM webhook_config WHERE config_key = ?";
        return $this->db->delete($sql, [$key]);
    }
    
    /**
     * Get configuration as associative array
     */
    public function getAsArray() {
        $configs = $this->getAll();
        $result = [];
        
        foreach ($configs as $config) {
            $result[$config['config_key']] = $config['config_value'];
        }
        
        return $result;
    }
    
    /**
     * Update multiple configurations at once
     */
    public function updateMultiple($configs) {
        $this->db->beginTransaction();
        
        try {
            foreach ($configs as $key => $value) {
                $this->set($key, $value);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get webhook URL with proper formatting
     */
    public function getWebhookUrl() {
        $url = $this->get('webhook_url');
        
        if (!$url) {
            // Generate default URL based on current request
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $path = dirname($_SERVER['REQUEST_URI'] ?? '') . '/api/webhook.php';
            $url = $protocol . '://' . $host . $path;
        }
        
        return $url;
    }
    
    /**
     * Validate configuration values
     */
    public function validate($key, $value) {
        $errors = [];
        
        switch ($key) {
            case 'max_message_size':
                if (!is_numeric($value) || $value < 1024) {
                    $errors[] = 'Max message size must be a number >= 1024 bytes';
                }
                break;
                
            case 'rate_limit_per_minute':
                if (!is_numeric($value) || $value < 1) {
                    $errors[] = 'Rate limit must be a positive number';
                }
                break;
                
            case 'webhook_url':
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    $errors[] = 'Webhook URL must be a valid URL';
                }
                break;
                
            case 'require_api_key':
            case 'log_all_requests':
            case 'auto_process_messages':
                if (!in_array(strtolower($value), ['true', 'false', '1', '0'])) {
                    $errors[] = 'Value must be true or false';
                }
                break;
        }
        
        return $errors;
    }
    
    /**
     * Get boolean configuration value
     */
    public function getBool($key, $default = false) {
        $value = $this->get($key, $default);
        return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
    }
    
    /**
     * Get integer configuration value
     */
    public function getInt($key, $default = 0) {
        $value = $this->get($key, $default);
        return (int)$value;
    }
}

/**
 * WebhookLog Model
 * 
 * Handles logging of webhook requests and responses
 */
class WebhookLog {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Log a webhook request
     */
    public function log($data) {
        $sql = "INSERT INTO webhook_logs (
            endpoint, method, ip_address, user_agent, 
            request_headers, request_body, response_code, 
            response_message, processing_time_ms
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['endpoint'] ?? $_SERVER['REQUEST_URI'] ?? '',
            $data['method'] ?? $_SERVER['REQUEST_METHOD'] ?? 'GET',
            $data['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? null,
            $data['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? null,
            json_encode($data['request_headers'] ?? []),
            $data['request_body'] ?? '',
            $data['response_code'] ?? 200,
            $data['response_message'] ?? '',
            $data['processing_time_ms'] ?? 0
        ];
        
        return $this->db->insert($sql, $params);
    }
    
    /**
     * Get recent logs
     */
    public function getRecent($limit = 100) {
        $sql = "SELECT * FROM webhook_logs ORDER BY created_at DESC LIMIT ?";
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    /**
     * Clean old logs
     */
    public function cleanup($daysOld = 7) {
        $sql = "DELETE FROM webhook_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        return $this->db->delete($sql, [$daysOld]);
    }
}
?>
