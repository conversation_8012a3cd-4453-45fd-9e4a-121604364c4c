package com.mtc.addmoney;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.telephony.SmsManager;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * SMS sending functionality with delivery tracking and queue integration
 */
public class SmsSender {
    
    private static final String TAG = "SmsSender";
    private static final String ACTION_SMS_SENT = "com.mtc.addmoney.SMS_SENT";
    private static final String ACTION_SMS_DELIVERED = "com.mtc.addmoney.SMS_DELIVERED";
    private static final String EXTRA_QUEUE_ITEM_ID = "queue_item_id";
    
    private final Context context;
    private final SmsQueueManager queueManager;
    private BroadcastReceiver sentReceiver;
    private BroadcastReceiver deliveredReceiver;
    
    public SmsSender(Context context) {
        this.context = context;
        this.queueManager = new SmsQueueManager(context);
        setupReceivers();
    }
    
    /**
     * Send SMS immediately (if possible) or queue for later
     */
    public void sendSms(String phoneNumber, String message) {
        Log.d(TAG, "Sending SMS to: " + phoneNumber);
        
        // Queue the SMS first
        SmsQueueItem queueItem = queueManager.queueSms(phoneNumber, message);
        
        // Try to send immediately
        try {
            sendSmsNow(queueItem);
        } catch (Exception e) {
            Log.e(TAG, "Failed to send SMS immediately, will retry later", e);
            queueManager.markSmsFailed(queueItem.getId());
        }
    }
    
    /**
     * Process all pending SMS in the queue
     */
    public void processPendingSms() {
        List<SmsQueueItem> pendingSms = queueManager.getPendingSms();
        Log.d(TAG, "Processing " + pendingSms.size() + " pending SMS");
        
        for (SmsQueueItem item : pendingSms) {
            try {
                sendSmsNow(item);
            } catch (Exception e) {
                Log.e(TAG, "Failed to send queued SMS: " + item.getId(), e);
                queueManager.markSmsFailed(item.getId());
            }
        }
    }
    
    /**
     * Send SMS immediately using SmsManager
     */
    private void sendSmsNow(SmsQueueItem queueItem) {
        SmsManager smsManager = SmsManager.getDefault();
        String phoneNumber = queueItem.getRecipient();
        String message = queueItem.getMessage();
        
        // Create pending intents for delivery tracking
        Intent sentIntent = new Intent(ACTION_SMS_SENT);
        sentIntent.putExtra(EXTRA_QUEUE_ITEM_ID, queueItem.getId());
        PendingIntent sentPendingIntent = PendingIntent.getBroadcast(
            context, 
            queueItem.getId().hashCode(), 
            sentIntent, 
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE : 
                PendingIntent.FLAG_UPDATE_CURRENT
        );
        
        Intent deliveredIntent = new Intent(ACTION_SMS_DELIVERED);
        deliveredIntent.putExtra(EXTRA_QUEUE_ITEM_ID, queueItem.getId());
        PendingIntent deliveredPendingIntent = PendingIntent.getBroadcast(
            context, 
            queueItem.getId().hashCode() + 1, 
            deliveredIntent, 
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE : 
                PendingIntent.FLAG_UPDATE_CURRENT
        );
        
        // Handle long messages by splitting them
        if (message.length() > 160) {
            ArrayList<String> parts = smsManager.divideMessage(message);
            ArrayList<PendingIntent> sentIntents = new ArrayList<>();
            ArrayList<PendingIntent> deliveredIntents = new ArrayList<>();
            
            for (int i = 0; i < parts.size(); i++) {
                sentIntents.add(sentPendingIntent);
                deliveredIntents.add(deliveredPendingIntent);
            }
            
            smsManager.sendMultipartTextMessage(phoneNumber, null, parts, sentIntents, deliveredIntents);
        } else {
            smsManager.sendTextMessage(phoneNumber, null, message, sentPendingIntent, deliveredPendingIntent);
        }
        
        Log.d(TAG, "SMS sent to SmsManager for: " + phoneNumber);
    }
    
    /**
     * Setup broadcast receivers for SMS delivery tracking
     */
    private void setupReceivers() {
        // SMS sent receiver
        sentReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String queueItemId = intent.getStringExtra(EXTRA_QUEUE_ITEM_ID);
                int resultCode = getResultCode();
                
                switch (resultCode) {
                    case android.app.Activity.RESULT_OK:
                        Log.d(TAG, "SMS sent successfully: " + queueItemId);
                        queueManager.markSmsSent(queueItemId);
                        break;
                    case SmsManager.RESULT_ERROR_GENERIC_FAILURE:
                        Log.e(TAG, "SMS generic failure: " + queueItemId);
                        queueManager.markSmsFailed(queueItemId);
                        break;
                    case SmsManager.RESULT_ERROR_NO_SERVICE:
                        Log.e(TAG, "SMS no service: " + queueItemId);
                        queueManager.markSmsFailed(queueItemId);
                        break;
                    case SmsManager.RESULT_ERROR_NULL_PDU:
                        Log.e(TAG, "SMS null PDU: " + queueItemId);
                        queueManager.markSmsFailed(queueItemId);
                        break;
                    case SmsManager.RESULT_ERROR_RADIO_OFF:
                        Log.e(TAG, "SMS radio off: " + queueItemId);
                        queueManager.markSmsFailed(queueItemId);
                        break;
                    default:
                        Log.e(TAG, "SMS unknown error: " + resultCode + " for " + queueItemId);
                        queueManager.markSmsFailed(queueItemId);
                        break;
                }
            }
        };
        
        // SMS delivered receiver
        deliveredReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String queueItemId = intent.getStringExtra(EXTRA_QUEUE_ITEM_ID);
                int resultCode = getResultCode();
                
                switch (resultCode) {
                    case android.app.Activity.RESULT_OK:
                        Log.d(TAG, "SMS delivered successfully: " + queueItemId);
                        break;
                    case android.app.Activity.RESULT_CANCELED:
                        Log.w(TAG, "SMS delivery cancelled: " + queueItemId);
                        break;
                    default:
                        Log.w(TAG, "SMS delivery unknown result: " + resultCode + " for " + queueItemId);
                        break;
                }
            }
        };
        
        // Register receivers
        IntentFilter sentFilter = new IntentFilter(ACTION_SMS_SENT);
        IntentFilter deliveredFilter = new IntentFilter(ACTION_SMS_DELIVERED);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(sentReceiver, sentFilter, Context.RECEIVER_NOT_EXPORTED);
            context.registerReceiver(deliveredReceiver, deliveredFilter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            context.registerReceiver(sentReceiver, sentFilter);
            context.registerReceiver(deliveredReceiver, deliveredFilter);
        }
    }
    
    /**
     * Cleanup receivers when done
     */
    public void cleanup() {
        try {
            if (sentReceiver != null) {
                context.unregisterReceiver(sentReceiver);
            }
            if (deliveredReceiver != null) {
                context.unregisterReceiver(deliveredReceiver);
            }
        } catch (IllegalArgumentException e) {
            // Receivers already unregistered
            Log.d(TAG, "Receivers already unregistered");
        }
    }
    
    /**
     * Check if SMS functionality is available
     */
    public static boolean isSmsAvailable(Context context) {
        try {
            SmsManager smsManager = SmsManager.getDefault();
            return smsManager != null;
        } catch (Exception e) {
            Log.e(TAG, "SMS not available", e);
            return false;
        }
    }
    
    /**
     * Get SMS queue statistics
     */
    public SmsQueueManager.QueueStats getQueueStats() {
        return queueManager.getStats();
    }
    
    /**
     * Retry failed SMS
     */
    public void retryFailedSms() {
        queueManager.retryFailedItems();
        processPendingSms();
    }
    
    /**
     * Clean up old SMS queue items
     */
    public void cleanupOldItems(int daysToKeep) {
        queueManager.cleanupOldItems(daysToKeep);
    }
}
