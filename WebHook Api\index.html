<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook API Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-badge {
            font-size: 0.8em;
        }
        .message-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .webhook-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .stats-card {
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: block;
        }
        .alert-dismissible {
            position: relative;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-webhook me-2"></i>
                Webhook API Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="connectionStatus">
                    <i class="fas fa-circle text-success me-1"></i>
                    Connected
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Alert Container -->
        <div id="alertContainer"></div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Messages</h6>
                                <h3 id="totalMessages">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-envelope fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Processed</h6>
                                <h3 id="processedMessages">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Pending</h6>
                                <h3 id="pendingMessages">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Failed</h6>
                                <h3 id="failedMessages">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="messages-tab" data-bs-toggle="tab" data-bs-target="#messages" type="button" role="tab">
                    <i class="fas fa-list me-1"></i>Messages
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab">
                    <i class="fas fa-cog me-1"></i>Configuration
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="testing-tab" data-bs-toggle="tab" data-bs-target="#testing" type="button" role="tab">
                    <i class="fas fa-vial me-1"></i>Testing
                </button>
            </li>
        </ul>

        <div class="tab-content" id="mainTabContent">
            <!-- Messages Tab -->
            <div class="tab-pane fade show active" id="messages" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Messages</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshMessages()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="senderFilter" placeholder="Filter by sender">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="received">Received</option>
                                    <option value="processed">Processed</option>
                                    <option value="failed">Failed</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary" onclick="applyFilters()">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                            </div>
                        </div>

                        <!-- Messages Table -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Sender</th>
                                        <th>Message Preview</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Received</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="messagesTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <div class="loading show">
                                                <i class="fas fa-spinner fa-spin"></i> Loading messages...
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <nav aria-label="Messages pagination">
                            <ul class="pagination justify-content-center" id="messagesPagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Configuration Tab -->
            <div class="tab-pane fade" id="config" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Webhook Configuration</h5>
                    </div>
                    <div class="card-body">
                        <form id="configForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="webhookUrl" class="form-label">Webhook URL</label>
                                        <input type="url" class="form-control" id="webhookUrl" required>
                                        <div class="form-text">The URL where webhook messages will be received</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="maxMessageSize" class="form-label">Max Message Size (bytes)</label>
                                        <input type="number" class="form-control" id="maxMessageSize" min="1024">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="rateLimitPerMinute" class="form-label">Rate Limit (per minute)</label>
                                        <input type="number" class="form-control" id="rateLimitPerMinute" min="1">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="requireApiKey">
                                            <label class="form-check-label" for="requireApiKey">
                                                Require API Key Authentication
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="logAllRequests">
                                            <label class="form-check-label" for="logAllRequests">
                                                Log All Requests
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="autoProcessMessages">
                                            <label class="form-check-label" for="autoProcessMessages">
                                                Auto Process Messages
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="loadConfiguration()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Testing Tab -->
            <div class="tab-pane fade" id="testing" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Webhook Testing</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label for="testWebhookUrl" class="form-label">Test Webhook URL</label>
                                <input type="url" class="form-control" id="testWebhookUrl" required>
                                <div class="form-text">URL to send test messages to</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="testSender" class="form-label">Test Sender</label>
                                        <input type="text" class="form-control" id="testSender" value="test_sender">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="testMessage" class="form-label">Test Message</label>
                                        <input type="text" class="form-control" id="testMessage" value="Test message from webhook API">
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-paper-plane"></i> Send Test Message
                            </button>
                        </form>
                        
                        <div id="testResults" class="mt-4" style="display: none;">
                            <h6>Test Results:</h6>
                            <pre id="testResultsContent" class="bg-light p-3 rounded"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Message Details Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Message Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- Message details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
