package com.mtc.addmoney;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import androidx.work.Worker;
import androidx.work.WorkerParameters;
import androidx.work.ListenableWorker;
import androidx.work.Data;
import androidx.work.Constraints;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;

import com.mtc.addmoney.ssl.TLSSocketFactory;

import org.apache.http.conn.ssl.AllowAllHostnameVerifier;
import org.json.JSONObject;

import java.io.BufferedWriter;
import java.io.OutputStreamWriter;
import java.io.BufferedOutputStream;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;

import javax.net.ssl.HttpsURLConnection;

public class WebHookWorkRequest extends Worker {

    public static final String DATA_URL = "URL";
    public static final String DATA_TEXT = "TEXT";
    public static final String DATA_HEADERS = "HEADERS";
    public static final String DATA_IGNORE_SSL = "IGNORE_SSL";
    public static final String DATA_LOG_ID = "LOG_ID";
    public static final String DATA_SENDER = "SENDER";

    public static final int MAX_ATTEMPT = 10;

    public static final String RESULT_SUCCESS = "success";
    public static final String RESULT_ERROR = "error";
    public static final String RESULT_RETRY = "error_retry";

    private static final String TAG = "SmsGateway";

    public WebHookWorkRequest(Context context, WorkerParameters workerParams) {
        super(context, workerParams);
    }

    /**
     * Inner class to hold webhook request result with response details
     */
    private static class WebhookResult {
        public final String status;
        public final int responseCode;
        public final String responseMessage;

        public WebhookResult(String status, int responseCode, String responseMessage) {
            this.status = status;
            this.responseCode = responseCode;
            this.responseMessage = responseMessage;
        }
    }

    @Override
    public ListenableWorker.Result doWork() {
        if (getRunAttemptCount() > MAX_ATTEMPT) {
            // Mark as failed if max attempts reached
            String logId = getInputData().getString(DATA_LOG_ID);
            if (logId != null) {
                NotificationLogManager logManager = new NotificationLogManager(getApplicationContext());
                logManager.markAsFailed(logId, 0, "Max retry attempts exceeded");
            }
            return ListenableWorker.Result.failure();
        }

        String url = getInputData().getString(DATA_URL);
        String jsonBody = getInputData().getString(DATA_TEXT);
        String headers = getInputData().getString(DATA_HEADERS);
        boolean ignoreSsl = getInputData().getBoolean(DATA_IGNORE_SSL, false);
        String logId = getInputData().getString(DATA_LOG_ID);

        // Update log for retry attempts
        if (logId != null && getRunAttemptCount() > 1) {
            NotificationLogManager logManager = new NotificationLogManager(getApplicationContext());
            logManager.markAsRetry(logId, getRunAttemptCount());
        }

        try {
            WebhookResult result = makeRequest(url, jsonBody, headers, ignoreSsl);

            // Update notification log based on result
            if (logId != null) {
                NotificationLogManager logManager = new NotificationLogManager(getApplicationContext());
                switch (result.status) {
                    case RESULT_SUCCESS:
                        logManager.markAsSuccess(logId, result.responseCode, result.responseMessage);
                        break;
                    case RESULT_ERROR:
                        logManager.markAsFailed(logId, result.responseCode, result.responseMessage);
                        break;
                    case RESULT_RETRY:
                        // Will be handled on next retry
                        break;
                }
            }

            switch (result.status) {
                case RESULT_SUCCESS:
                    return ListenableWorker.Result.success();
                case RESULT_RETRY:
                    return ListenableWorker.Result.retry();
                default:
                    return ListenableWorker.Result.failure();
            }

        } catch (Exception e) {
            Log.e(TAG, "Fatal error: ", e);
            if (logId != null) {
                NotificationLogManager logManager = new NotificationLogManager(getApplicationContext());
                logManager.markAsFailed(logId, 0, "Fatal error: " + e.getMessage());
            }
            return ListenableWorker.Result.failure();
        }
    }

    private WebhookResult makeRequest(String urlString, String jsonBody, String headersJson, boolean ignoreSsl) {
        HttpURLConnection connection = null;

        try {
            Log.i(TAG, "Sending request to: " + urlString);

            URL url = new URL(urlString);
            connection = (HttpURLConnection) url.openConnection();

            // Handle SSL
            if (connection instanceof HttpsURLConnection) {
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                httpsConnection.setSSLSocketFactory(new TLSSocketFactory(ignoreSsl));
                if (ignoreSsl) {
                    httpsConnection.setHostnameVerifier(new AllowAllHostnameVerifier());
                }
            }

            // Setup connection
            connection.setDoOutput(true);
            connection.setChunkedStreamingMode(0);
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");

            // Apply custom headers
            JSONObject headerObj = new JSONObject(headersJson);
            Iterator<String> keys = headerObj.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                Object value = headerObj.get(key);
                if (value instanceof String) {
                    connection.setRequestProperty(key, (String) value);
                } else {
                    Log.w(TAG, "Skipping non-string header: " + key);
                }
            }

            // Write body
            try (
                    BufferedOutputStream outputStream = new BufferedOutputStream(connection.getOutputStream());
                    BufferedWriter writer = new BufferedWriter(
                            new OutputStreamWriter(outputStream, Build.VERSION.SDK_INT >= 19 ? StandardCharsets.UTF_8 : null)
                    )
            ) {
                writer.write(jsonBody);
                writer.flush();
            }

            // Optional: read response to trigger execution
            try (BufferedInputStream inputStream = new BufferedInputStream(connection.getInputStream())) {
                // Read or ignore, just to complete the request
                inputStream.read(); // Blocking call ensures stream is opened
            }

            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();
            Log.i(TAG, "Response code: " + responseCode + ", message: " + responseMessage);

            if (String.valueOf(responseCode).startsWith("2")) {
                return new WebhookResult(RESULT_SUCCESS, responseCode, responseMessage);
            } else {
                return new WebhookResult(RESULT_ERROR, responseCode, responseMessage);
            }

        } catch (IOException e) {
            Log.e(TAG, "Network error: ", e);
            return new WebhookResult(RESULT_RETRY, 0, "Network error: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Request error: ", e);
            return new WebhookResult(RESULT_ERROR, 0, "Request error: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}
