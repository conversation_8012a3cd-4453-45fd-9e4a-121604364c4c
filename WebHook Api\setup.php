<?php
/**
 * Webhook API Setup Script
 * 
 * This script sets up the database and initial configuration
 */

// Set execution time limit for setup
set_time_limit(300);

// Include database configuration
require_once __DIR__ . '/config/database.php';

// HTML header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook API Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .setup-container { max-width: 800px; margin: 50px auto; }
        .log-output { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container setup-container">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-cog"></i>
                    Webhook API Setup
                </h3>
            </div>
            <div class="card-body">
                <?php
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
                    runSetup();
                } else {
                    showSetupForm();
                }
                ?>
            </div>
        </div>
    </div>
</body>
</html>

<?php

function showSetupForm() {
    ?>
    <div class="alert alert-info">
        <h5>Welcome to Webhook API Setup</h5>
        <p>This setup will:</p>
        <ul>
            <li>Create the database and tables</li>
            <li>Set up initial configuration</li>
            <li>Create default API keys</li>
            <li>Test database connectivity</li>
        </ul>
        <p><strong>Note:</strong> Make sure your database credentials are correct in <code>config/database.php</code></p>
    </div>
    
    <form method="post">
        <div class="mb-3">
            <label for="dbHost" class="form-label">Database Host</label>
            <input type="text" class="form-control" id="dbHost" name="db_host" value="<?php echo DB_HOST; ?>" required>
        </div>
        
        <div class="mb-3">
            <label for="dbName" class="form-label">Database Name</label>
            <input type="text" class="form-control" id="dbName" name="db_name" value="<?php echo DB_NAME; ?>" required>
        </div>
        
        <div class="mb-3">
            <label for="dbUser" class="form-label">Database User</label>
            <input type="text" class="form-control" id="dbUser" name="db_user" value="<?php echo DB_USER; ?>" required>
        </div>
        
        <div class="mb-3">
            <label for="dbPass" class="form-label">Database Password</label>
            <input type="password" class="form-control" id="dbPass" name="db_pass" value="<?php echo DB_PASS; ?>">
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="dropExisting" name="drop_existing">
                <label class="form-check-label" for="dropExisting">
                    Drop existing tables (WARNING: This will delete all data)
                </label>
            </div>
        </div>
        
        <button type="submit" name="setup" class="btn btn-primary">
            <i class="fas fa-play"></i> Run Setup
        </button>
    </form>
    <?php
}

function runSetup() {
    echo '<div class="log-output" id="setupLog">';
    
    try {
        logMessage("Starting Webhook API setup...", 'info');
        
        // Test database connection
        logMessage("Testing database connection...", 'info');
        $db = Database::getInstance();
        
        if (!$db->isConnected()) {
            throw new Exception("Cannot connect to database");
        }
        logMessage("✓ Database connection successful", 'success');
        
        // Read and execute SQL setup file
        logMessage("Setting up database schema...", 'info');
        $sqlFile = __DIR__ . '/database/setup.sql';
        
        if (!file_exists($sqlFile)) {
            throw new Exception("Setup SQL file not found: $sqlFile");
        }
        
        $sql = file_get_contents($sqlFile);
        
        // Handle drop existing tables if requested
        if (isset($_POST['drop_existing']) && $_POST['drop_existing']) {
            logMessage("Dropping existing tables...", 'warning');
            $dropSql = "
                DROP TABLE IF EXISTS webhook_logs;
                DROP TABLE IF EXISTS api_keys;
                DROP TABLE IF EXISTS webhook_config;
                DROP TABLE IF EXISTS webhook_messages;
                DROP VIEW IF EXISTS recent_messages;
                DROP VIEW IF EXISTS message_stats;
            ";
            
            $db->getConnection()->exec($dropSql);
            logMessage("✓ Existing tables dropped", 'success');
        }
        
        // Execute setup SQL
        $db->getConnection()->exec($sql);
        logMessage("✓ Database schema created successfully", 'success');
        
        // Test table creation
        logMessage("Verifying table creation...", 'info');
        $tables = ['webhook_messages', 'webhook_config', 'api_keys', 'webhook_logs'];
        
        foreach ($tables as $table) {
            $result = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
            if ($result) {
                logMessage("✓ Table '$table' created successfully", 'success');
            } else {
                throw new Exception("Table '$table' was not created");
            }
        }
        
        // Create storage directories
        logMessage("Creating storage directories...", 'info');
        $storageDir = __DIR__ . '/storage';
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
            logMessage("✓ Storage directory created", 'success');
        } else {
            logMessage("✓ Storage directory already exists", 'success');
        }
        
        // Set up default webhook URL
        logMessage("Configuring default webhook URL...", 'info');
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['REQUEST_URI']) . '/api/webhook.php';
        $webhookUrl = $protocol . '://' . $host . $path;
        
        $config = new WebhookConfig();
        $config->set('webhook_url', $webhookUrl, 'Main webhook endpoint URL');
        logMessage("✓ Default webhook URL set to: $webhookUrl", 'success');
        
        // Test webhook endpoint
        logMessage("Testing webhook endpoint...", 'info');
        $testResult = testWebhookEndpoint($webhookUrl);
        if ($testResult['success']) {
            logMessage("✓ Webhook endpoint is accessible", 'success');
        } else {
            logMessage("⚠ Webhook endpoint test failed: " . $testResult['error'], 'warning');
        }
        
        logMessage("\n" . str_repeat("=", 50), 'info');
        logMessage("SETUP COMPLETED SUCCESSFULLY!", 'success');
        logMessage("", 'info');
        logMessage("Your webhook API is now ready to use:", 'info');
        logMessage("• Dashboard: " . $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']) . '/index.html', 'info');
        logMessage("• Webhook URL: $webhookUrl", 'info');
        logMessage("• API Documentation: Check the README.md file", 'info');
        logMessage("", 'info');
        logMessage("Next steps:", 'info');
        logMessage("1. Configure your Android app to send webhooks to: $webhookUrl", 'info');
        logMessage("2. Access the dashboard to monitor incoming messages", 'info');
        logMessage("3. Test the webhook using the testing interface", 'info');
        
    } catch (Exception $e) {
        logMessage("✗ Setup failed: " . $e->getMessage(), 'error');
        logMessage("Please check your database configuration and try again.", 'error');
    }
    
    echo '</div>';
    
    echo '<div class="mt-3">';
    echo '<a href="index.html" class="btn btn-success">Go to Dashboard</a> ';
    echo '<a href="setup.php" class="btn btn-secondary">Run Setup Again</a>';
    echo '</div>';
}

function logMessage($message, $type = 'info') {
    $timestamp = date('Y-m-d H:i:s');
    $class = $type;
    echo "<span class=\"$class\">[$timestamp] $message</span>\n";
    
    // Flush output for real-time display
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

function testWebhookEndpoint($url) {
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'GET'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return ['success' => false, 'error' => $error];
        }
        
        if ($httpCode === 200) {
            return ['success' => true, 'response' => $response];
        } else {
            return ['success' => false, 'error' => "HTTP $httpCode"];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Include required models for setup
require_once __DIR__ . '/models/WebhookConfig.php';
?>
