<?php
/**
 * Webhook Tester Utility
 * 
 * A standalone script for testing webhook functionality
 */

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/WebhookConfig.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Get configuration
$config = new WebhookConfig();
$webhookUrl = $config->getWebhookUrl();

// Handle different test types
$testType = $_GET['test'] ?? 'basic';

switch ($testType) {
    case 'basic':
        runBasicTest($webhookUrl);
        break;
    case 'load':
        runLoadTest($webhookUrl);
        break;
    case 'android':
        runAndroidCompatibilityTest($webhookUrl);
        break;
    case 'status':
        checkWebhookStatus($webhookUrl);
        break;
    default:
        echo json_encode(['error' => 'Invalid test type']);
}

/**
 * Run basic webhook test
 */
function runBasicTest($webhookUrl) {
    $testMessage = [
        'id' => 'test_' . uniqid(),
        'from' => '+1234567890',
        'text' => 'Test message from webhook tester',
        'timestamp' => time(),
        'type' => 'sms'
    ];
    
    $result = sendWebhook($webhookUrl, $testMessage);
    
    echo json_encode([
        'test_type' => 'basic',
        'webhook_url' => $webhookUrl,
        'payload_sent' => $testMessage,
        'result' => $result,
        'success' => $result['http_code'] >= 200 && $result['http_code'] < 300
    ], JSON_PRETTY_PRINT);
}

/**
 * Run load test with multiple messages
 */
function runLoadTest($webhookUrl) {
    $messageCount = min(50, max(1, (int)($_GET['count'] ?? 10)));
    $results = [];
    $successCount = 0;
    $startTime = microtime(true);
    
    for ($i = 1; $i <= $messageCount; $i++) {
        $testMessage = [
            'id' => 'load_test_' . $i . '_' . uniqid(),
            'from' => '+123456789' . ($i % 10),
            'text' => "Load test message #$i",
            'timestamp' => time(),
            'type' => 'sms'
        ];
        
        $result = sendWebhook($webhookUrl, $testMessage);
        $results[] = [
            'message_id' => $testMessage['id'],
            'http_code' => $result['http_code'],
            'success' => $result['http_code'] >= 200 && $result['http_code'] < 300,
            'response_time_ms' => $result['response_time_ms']
        ];
        
        if ($result['http_code'] >= 200 && $result['http_code'] < 300) {
            $successCount++;
        }
        
        // Small delay to avoid overwhelming the server
        usleep(100000); // 100ms
    }
    
    $totalTime = (microtime(true) - $startTime) * 1000;
    
    echo json_encode([
        'test_type' => 'load',
        'webhook_url' => $webhookUrl,
        'messages_sent' => $messageCount,
        'successful_messages' => $successCount,
        'failed_messages' => $messageCount - $successCount,
        'success_rate' => round(($successCount / $messageCount) * 100, 2) . '%',
        'total_time_ms' => round($totalTime, 2),
        'average_time_per_message_ms' => round($totalTime / $messageCount, 2),
        'results' => $results
    ], JSON_PRETTY_PRINT);
}

/**
 * Test Android SMS Gateway compatibility
 */
function runAndroidCompatibilityTest($webhookUrl) {
    $testMessages = [
        // Standard SMS format
        [
            'id' => 'android_test_1',
            'from' => '+1234567890',
            'text' => 'Standard SMS message',
            'timestamp' => time(),
            'sim' => '1'
        ],
        // Alternative field names
        [
            'message_id' => 'android_test_2',
            'sender' => '+0987654321',
            'content' => 'Alternative field names test',
            'received_at' => date('c'),
            'sim_slot' => '2'
        ],
        // Unicode content
        [
            'id' => 'android_test_3',
            'from' => '+1122334455',
            'text' => 'Unicode test: 🚀 Hello 世界 مرحبا',
            'timestamp' => time()
        ],
        // Long message
        [
            'id' => 'android_test_4',
            'from' => '+5566778899',
            'text' => str_repeat('This is a long message test. ', 20),
            'timestamp' => time()
        ]
    ];
    
    $results = [];
    $successCount = 0;
    
    foreach ($testMessages as $index => $message) {
        $result = sendWebhook($webhookUrl, $message);
        $success = $result['http_code'] >= 200 && $result['http_code'] < 300;
        
        $results[] = [
            'test_case' => $index + 1,
            'message_type' => getTestCaseDescription($index),
            'payload' => $message,
            'http_code' => $result['http_code'],
            'success' => $success,
            'response' => $result['response'],
            'response_time_ms' => $result['response_time_ms']
        ];
        
        if ($success) {
            $successCount++;
        }
    }
    
    echo json_encode([
        'test_type' => 'android_compatibility',
        'webhook_url' => $webhookUrl,
        'total_tests' => count($testMessages),
        'successful_tests' => $successCount,
        'failed_tests' => count($testMessages) - $successCount,
        'compatibility_score' => round(($successCount / count($testMessages)) * 100, 2) . '%',
        'results' => $results
    ], JSON_PRETTY_PRINT);
}

/**
 * Check webhook status and connectivity
 */
function checkWebhookStatus($webhookUrl) {
    $startTime = microtime(true);
    
    // Test GET request to webhook endpoint
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $webhookUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'GET'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $responseTime = (microtime(true) - $startTime) * 1000;
    $error = curl_error($ch);
    curl_close($ch);
    
    $status = [
        'webhook_url' => $webhookUrl,
        'accessible' => $httpCode === 200,
        'http_code' => $httpCode,
        'response_time_ms' => round($responseTime, 2),
        'error' => $error ?: null,
        'response' => $response ? json_decode($response, true) : null,
        'timestamp' => date('c')
    ];
    
    // Additional checks
    $status['ssl_enabled'] = strpos($webhookUrl, 'https://') === 0;
    $status['localhost'] = strpos($webhookUrl, 'localhost') !== false || strpos($webhookUrl, '127.0.0.1') !== false;
    
    // Database connectivity check
    try {
        $db = Database::getInstance();
        $status['database_connected'] = $db->isConnected();
    } catch (Exception $e) {
        $status['database_connected'] = false;
        $status['database_error'] = $e->getMessage();
    }
    
    echo json_encode($status, JSON_PRETTY_PRINT);
}

/**
 * Send webhook request
 */
function sendWebhook($url, $payload) {
    $startTime = microtime(true);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($payload),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'User-Agent: WebhookTester/1.0'
        ],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $responseTime = (microtime(true) - $startTime) * 1000;
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'response_time_ms' => round($responseTime, 2),
        'error' => $error ?: null
    ];
}

/**
 * Get test case description
 */
function getTestCaseDescription($index) {
    $descriptions = [
        'Standard SMS format',
        'Alternative field names',
        'Unicode content',
        'Long message'
    ];
    
    return $descriptions[$index] ?? 'Unknown test case';
}
?>
