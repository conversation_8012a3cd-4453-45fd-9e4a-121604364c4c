package com.mtc.addmoney;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.NotificationCompat;

/**
 * Background service for handling SMS operations independently of app state
 * This service runs as a foreground service on Android 8+ to ensure reliability
 */
public class BackgroundSmsService extends Service {
    
    private static final String TAG = "BackgroundSmsService";
    private static final String CHANNEL_ID = "SmsBackgroundService";
    private static final int NOTIFICATION_ID = 2;
    private static final long QUEUE_PROCESSING_INTERVAL = 30000; // 30 seconds
    
    private Handler handler;
    private Runnable queueProcessorRunnable;
    private SmsSender smsSender;
    private SmsQueueManager queueManager;
    private boolean isServiceRunning = false;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Background SMS service created");
        
        smsSender = new SmsSender(this);
        queueManager = new SmsQueueManager(this);
        handler = new Handler(Looper.getMainLooper());
        
        createNotificationChannel();
        startForeground(NOTIFICATION_ID, createNotification());
        
        startQueueProcessor();
        isServiceRunning = true;
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Background SMS service started");
        
        if (!isServiceRunning) {
            startQueueProcessor();
            isServiceRunning = true;
        }
        
        // Process any immediate requests
        if (intent != null) {
            String action = intent.getStringExtra("action");
            if ("PROCESS_QUEUE".equals(action)) {
                processQueues();
            }
        }
        
        // Return START_STICKY to restart service if killed
        return START_STICKY;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "Background SMS service destroyed");
        
        isServiceRunning = false;
        
        if (handler != null && queueProcessorRunnable != null) {
            handler.removeCallbacks(queueProcessorRunnable);
        }
        
        if (smsSender != null) {
            smsSender.cleanup();
        }
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null; // This is a started service, not bound
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "SMS Background Service",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Handles SMS operations in the background");
            channel.setShowBadge(false);
            
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }
    
    private Notification createNotification() {
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_f)
                .setContentTitle("SMS Service Running")
                .setContentText("Processing SMS and webhooks in background")
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .setShowWhen(false);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            builder.setColor(getColor(R.color.colorPrimary));
        }
        
        return builder.build();
    }
    
    private void startQueueProcessor() {
        queueProcessorRunnable = new Runnable() {
            @Override
            public void run() {
                if (isServiceRunning) {
                    processQueues();
                    
                    // Schedule next processing
                    handler.postDelayed(this, QUEUE_PROCESSING_INTERVAL);
                }
            }
        };
        
        // Start processing immediately
        handler.post(queueProcessorRunnable);
    }
    
    private void processQueues() {
        try {
            Log.d(TAG, "Processing queues...");
            
            // Get queue statistics
            SmsQueueManager.QueueStats stats = queueManager.getStats();
            Log.d(TAG, "Queue stats - Pending: " + stats.pendingCount + 
                      ", SMS: " + stats.smsCount + 
                      ", Webhooks: " + stats.webhookCount);
            
            // Process pending SMS (SMS doesn't require internet)
            if (stats.pendingCount > 0) {
                smsSender.processPendingSms();
            }
            
            // Process pending webhooks if connected
            ConnectivityHelper connectivityHelper = new ConnectivityHelper(this);
            if (connectivityHelper.isConnected()) {
                processQueuedWebhooks();
            }
            
            // Clean up old items periodically (keep for 7 days)
            cleanupOldItems();
            
            // Update notification with current stats
            updateNotification(stats);
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing queues", e);
        }
    }
    
    private void processQueuedWebhooks() {
        // This is handled by WorkManager through ConnectivityChangeReceiver
        // We just log here for monitoring
        Log.d(TAG, "Webhooks will be processed by WorkManager when connectivity is available");
    }
    
    private void cleanupOldItems() {
        // Clean up items older than 7 days
        queueManager.cleanupOldItems(7);
        
        // Clean up old notification logs
        NotificationLogManager logManager = new NotificationLogManager(this);
        logManager.cleanupOldLogs(7);
    }
    
    private void updateNotification(SmsQueueManager.QueueStats stats) {
        String contentText;
        if (stats.pendingCount > 0) {
            contentText = "Processing " + stats.pendingCount + " pending items";
        } else {
            contentText = "Monitoring SMS and webhooks";
        }
        
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_f)
                .setContentTitle("SMS Service Running")
                .setContentText(contentText)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .setShowWhen(false)
                .build();
        
        NotificationManager notificationManager = getSystemService(NotificationManager.class);
        if (notificationManager != null) {
            notificationManager.notify(NOTIFICATION_ID, notification);
        }
    }
    
    /**
     * Public method to trigger immediate queue processing
     */
    public static void triggerQueueProcessing(android.content.Context context) {
        Intent intent = new Intent(context, BackgroundSmsService.class);
        intent.putExtra("action", "PROCESS_QUEUE");
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }
}
