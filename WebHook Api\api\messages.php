<?php
/**
 * Messages API Endpoint
 * 
 * Handles retrieval and management of webhook messages
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../models/WebhookMessage.php';
require_once __DIR__ . '/../utils/ApiResponse.php';

$webhookMessage = new WebhookMessage();
$response = new ApiResponse();

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? 'list';
    
    switch ($method) {
        case 'GET':
            handleGet($webhookMessage, $response, $action);
            break;
            
        case 'POST':
            handlePost($webhookMessage, $response, $action);
            break;
            
        case 'PUT':
            handlePut($webhookMessage, $response);
            break;
            
        case 'DELETE':
            handleDelete($webhookMessage, $response);
            break;
            
        default:
            $response->error('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Messages API error: " . $e->getMessage());
    $response->error('Internal server error', 500);
}

/**
 * Handle GET requests
 */
function handleGet($webhookMessage, $response, $action) {
    switch ($action) {
        case 'list':
            handleList($webhookMessage, $response);
            break;
            
        case 'stats':
            handleStats($webhookMessage, $response);
            break;
            
        case 'search':
            handleSearch($webhookMessage, $response);
            break;
            
        case 'get':
            handleGetSingle($webhookMessage, $response);
            break;
            
        default:
            $response->error('Invalid action', 400);
    }
}

/**
 * Handle message listing with pagination and filters
 */
function handleList($webhookMessage, $response) {
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(100, max(1, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // Build filters
    $filters = [];
    if (!empty($_GET['sender'])) {
        $filters['sender'] = $_GET['sender'];
    }
    if (!empty($_GET['status'])) {
        $filters['status'] = $_GET['status'];
    }
    if (!empty($_GET['message_type'])) {
        $filters['message_type'] = $_GET['message_type'];
    }
    if (!empty($_GET['date_from'])) {
        $filters['date_from'] = $_GET['date_from'];
    }
    if (!empty($_GET['date_to'])) {
        $filters['date_to'] = $_GET['date_to'];
    }
    
    $messages = $webhookMessage->getRecent($limit, $offset, $filters);
    $total = $webhookMessage->getCount($filters);
    
    // Format messages for display
    $formattedMessages = array_map(function($message) {
        return [
            'id' => $message['id'],
            'message_id' => $message['message_id'],
            'sender' => $message['sender'],
            'message_preview' => substr($message['message_content'], 0, 100) . (strlen($message['message_content']) > 100 ? '...' : ''),
            'message_type' => $message['message_type'],
            'status' => $message['status'],
            'received_at' => $message['received_at'],
            'processed_at' => $message['processed_at']
        ];
    }, $messages);
    
    $response->paginated($formattedMessages, $total, $page, $limit, 'Messages retrieved successfully');
}

/**
 * Handle statistics request
 */
function handleStats($webhookMessage, $response) {
    $days = min(90, max(1, (int)($_GET['days'] ?? 7)));
    $stats = $webhookMessage->getStats($days);
    
    $summary = [
        'total_messages' => array_sum(array_column($stats, 'total')),
        'processed_messages' => array_sum(array_column($stats, 'processed')),
        'failed_messages' => array_sum(array_column($stats, 'failed')),
        'pending_messages' => array_sum(array_column($stats, 'pending')),
        'daily_stats' => $stats
    ];
    
    $response->success($summary, 'Statistics retrieved successfully');
}

/**
 * Handle search request
 */
function handleSearch($webhookMessage, $response) {
    $query = $_GET['q'] ?? '';
    if (empty($query)) {
        $response->error('Search query is required', 400);
        return;
    }
    
    $limit = min(50, max(1, (int)($_GET['limit'] ?? 20)));
    $results = $webhookMessage->search($query, $limit);
    
    $response->success($results, 'Search completed successfully');
}

/**
 * Handle single message retrieval
 */
function handleGetSingle($webhookMessage, $response) {
    $id = $_GET['id'] ?? '';
    if (empty($id)) {
        $response->error('Message ID is required', 400);
        return;
    }
    
    $message = $webhookMessage->getById($id);
    if (!$message) {
        $response->error('Message not found', 404);
        return;
    }
    
    // Decode JSON fields for display
    $message['headers'] = json_decode($message['headers'], true);
    $message['raw_payload'] = json_decode($message['raw_payload'], true);
    
    $response->success($message, 'Message retrieved successfully');
}

/**
 * Handle POST requests (test message sending)
 */
function handlePost($webhookMessage, $response, $action) {
    if ($action === 'test') {
        handleTestMessage($response);
    } else {
        $response->error('Invalid action', 400);
    }
}

/**
 * Handle test message sending
 */
function handleTestMessage($response) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $response->error('Invalid JSON format', 400);
        return;
    }
    
    $webhookUrl = $input['webhook_url'] ?? '';
    $testMessage = $input['message'] ?? 'Test message from webhook API';
    $sender = $input['sender'] ?? 'test_sender';
    
    if (empty($webhookUrl)) {
        $response->error('Webhook URL is required', 400);
        return;
    }
    
    // Prepare test payload
    $payload = [
        'id' => 'test_' . uniqid(),
        'from' => $sender,
        'text' => $testMessage,
        'timestamp' => time(),
        'type' => 'test'
    ];
    
    // Send test webhook
    $result = sendTestWebhook($webhookUrl, $payload);
    
    if ($result['success']) {
        $response->success($result, 'Test message sent successfully');
    } else {
        $response->error('Failed to send test message', 500, $result);
    }
}

/**
 * Handle PUT requests (update message status)
 */
function handlePut($webhookMessage, $response) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $response->error('Invalid JSON format', 400);
        return;
    }
    
    $id = $input['id'] ?? '';
    $status = $input['status'] ?? '';
    $errorMessage = $input['error_message'] ?? null;
    
    if (empty($id) || empty($status)) {
        $response->error('ID and status are required', 400);
        return;
    }
    
    $updated = $webhookMessage->updateStatus($id, $status, $errorMessage);
    
    if ($updated > 0) {
        $response->success(['updated' => $updated], 'Message status updated successfully');
    } else {
        $response->error('Message not found or not updated', 404);
    }
}

/**
 * Handle DELETE requests (cleanup)
 */
function handleDelete($webhookMessage, $response) {
    $daysOld = max(1, (int)($_GET['days_old'] ?? 30));
    $deleted = $webhookMessage->deleteOldMessages($daysOld);
    
    $response->success(['deleted_count' => $deleted], "Deleted $deleted old messages");
}

/**
 * Send test webhook
 */
function sendTestWebhook($url, $payload) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($payload),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'User-Agent: WebhookAPI-Test/1.0'
        ],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'payload_sent' => $payload
    ];
}
?>
