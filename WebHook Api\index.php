<?php
/**
 * Webhook API Entry Point
 * 
 * This file provides a simple entry point and redirects to appropriate pages
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if setup is needed
$setupNeeded = false;
$configFile = __DIR__ . '/config/database.php';

if (!file_exists($configFile)) {
    $setupNeeded = true;
} else {
    // Check if database is set up
    try {
        require_once $configFile;
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, DB_OPTIONS);
        
        // Check if tables exist
        $stmt = $pdo->query("SHOW TABLES LIKE 'webhook_messages'");
        if ($stmt->rowCount() === 0) {
            $setupNeeded = true;
        }
    } catch (Exception $e) {
        $setupNeeded = true;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook API System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .welcome-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        .logo {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin: 10px 0;
            display: inline-block;
        }
        .status-setup-needed {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-ready {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">
            <i class="fas fa-webhook"></i>
        </div>
        
        <h1 class="mb-3">Webhook API System</h1>
        
        <?php if ($setupNeeded): ?>
            <div class="status-badge status-setup-needed">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Setup Required
            </div>
            
            <p class="text-muted mb-4">
                Welcome! Your webhook API system needs to be set up before you can start using it.
                This will create the database and configure the system.
            </p>
            
            <div class="d-flex justify-content-center flex-wrap">
                <a href="setup.php" class="btn-custom">
                    <i class="fas fa-cog me-2"></i>Run Setup
                </a>
                <a href="diagnostic.php" class="btn btn-outline-secondary">
                    <i class="fas fa-stethoscope me-2"></i>Run Diagnostics
                </a>
            </div>
            
        <?php else: ?>
            <div class="status-badge status-ready">
                <i class="fas fa-check-circle me-2"></i>
                System Ready
            </div>
            
            <p class="text-muted mb-4">
                Your webhook API system is set up and ready to receive messages.
                Use the dashboard to monitor and manage your webhooks.
            </p>
            
            <div class="d-flex justify-content-center flex-wrap">
                <a href="index.html" class="btn-custom">
                    <i class="fas fa-tachometer-alt me-2"></i>Open Dashboard
                </a>
                <a href="api/webhook.php" class="btn btn-outline-primary">
                    <i class="fas fa-link me-2"></i>Test Webhook
                </a>
                <a href="diagnostic.php" class="btn btn-outline-secondary">
                    <i class="fas fa-stethoscope me-2"></i>Diagnostics
                </a>
            </div>
        <?php endif; ?>
        
        <hr class="my-4">
        
        <div class="row text-start">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle me-2"></i>Quick Info</h6>
                <ul class="list-unstyled small text-muted">
                    <li><strong>Webhook URL:</strong><br>
                        <code><?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/webhook.php'; ?></code>
                    </li>
                    <li class="mt-2"><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                    <li><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-book me-2"></i>Documentation</h6>
                <ul class="list-unstyled small">
                    <li><a href="README.md" class="text-decoration-none">📖 Read Documentation</a></li>
                    <li><a href="test/webhook_tester.php" class="text-decoration-none">🧪 Advanced Testing</a></li>
                    <li><a href="api/" class="text-decoration-none">🔗 API Endpoints</a></li>
                </ul>
            </div>
        </div>
        
        <?php if (!$setupNeeded): ?>
        <div class="mt-4 p-3 bg-light rounded">
            <h6 class="mb-2">🚀 Next Steps:</h6>
            <ol class="text-start small text-muted mb-0">
                <li>Configure your Android SMS app to send webhooks to the URL above</li>
                <li>Use the dashboard to monitor incoming messages</li>
                <li>Test the webhook functionality using the testing tools</li>
            </ol>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
