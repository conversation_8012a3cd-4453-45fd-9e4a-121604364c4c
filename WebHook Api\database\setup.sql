-- Webhook API Database Setup
-- This script creates the necessary database and tables for the webhook system

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS webhook_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE webhook_api;

-- Table for storing webhook messages
CREATE TABLE IF NOT EXISTS webhook_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id VARCHAR(255) UNIQUE NOT NULL,
    sender VARCHAR(255) NOT NULL,
    message_content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'sms',
    headers JSON,
    raw_payload JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    status ENUM('received', 'processed', 'failed') DEFAULT 'received',
    error_message TEXT NULL,
    INDEX idx_sender (sender),
    INDEX idx_received_at (received_at),
    INDEX idx_status (status),
    INDEX idx_message_type (message_type)
) ENGINE=InnoDB;

-- Table for webhook configuration
CREATE TABLE IF NOT EXISTS webhook_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Table for API keys and authentication
CREATE TABLE IF NOT EXISTS api_keys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    INDEX idx_api_key (api_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB;

-- Table for webhook logs and statistics
CREATE TABLE IF NOT EXISTS webhook_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_headers JSON,
    request_body TEXT,
    response_code INT,
    response_message TEXT,
    processing_time_ms INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at),
    INDEX idx_response_code (response_code)
) ENGINE=InnoDB;

-- Insert default configuration values
INSERT INTO webhook_config (config_key, config_value, description) VALUES
('webhook_url', 'http://localhost/WebHook%20Api/api/webhook.php', 'Main webhook endpoint URL'),
('max_message_size', '10485760', 'Maximum message size in bytes (10MB)'),
('rate_limit_per_minute', '100', 'Maximum requests per minute per IP'),
('require_api_key', 'false', 'Whether API key authentication is required'),
('log_all_requests', 'true', 'Whether to log all incoming requests'),
('auto_process_messages', 'true', 'Whether to automatically process received messages')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP;

-- Create a default API key for testing
INSERT INTO api_keys (key_name, api_key, permissions) VALUES
('default_test_key', 'test_key_12345', '{"read": true, "write": true, "admin": false}')
ON DUPLICATE KEY UPDATE 
    key_name = VALUES(key_name),
    permissions = VALUES(permissions);

-- Create views for easier data access
CREATE OR REPLACE VIEW recent_messages AS
SELECT 
    id,
    message_id,
    sender,
    LEFT(message_content, 100) as message_preview,
    message_type,
    status,
    received_at,
    processed_at
FROM webhook_messages 
ORDER BY received_at DESC 
LIMIT 100;

CREATE OR REPLACE VIEW message_stats AS
SELECT 
    DATE(received_at) as date,
    COUNT(*) as total_messages,
    COUNT(CASE WHEN status = 'processed' THEN 1 END) as processed_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
    COUNT(CASE WHEN status = 'received' THEN 1 END) as pending_count
FROM webhook_messages 
WHERE received_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(received_at)
ORDER BY date DESC;
