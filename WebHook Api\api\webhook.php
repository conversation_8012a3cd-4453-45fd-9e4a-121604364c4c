<?php
/**
 * Webhook API Endpoint
 * 
 * This endpoint receives webhook messages and stores them in the database.
 * It's designed to be compatible with the Android SMS Gateway app.
 */

// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include required files
require_once __DIR__ . '/../models/WebhookMessage.php';
require_once __DIR__ . '/../models/WebhookConfig.php';
require_once __DIR__ . '/../utils/RateLimiter.php';
require_once __DIR__ . '/../utils/ApiResponse.php';

// Initialize components
$webhookMessage = new WebhookMessage();
$config = new WebhookConfig();
$rateLimiter = new RateLimiter();
$response = new ApiResponse();

// Start timing for performance logging
$startTime = microtime(true);

try {
    // Rate limiting check
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $rateLimit = $config->getInt('rate_limit_per_minute', 100);
    
    if (!$rateLimiter->checkLimit($clientIp, $rateLimit)) {
        $response->error('Rate limit exceeded', 429);
        exit();
    }
    
    // Only accept POST requests for webhook data
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            // Return status information for GET requests
            $response->success([
                'status' => 'Webhook endpoint is active',
                'timestamp' => date('c'),
                'version' => '1.0.0',
                'methods_allowed' => ['POST'],
                'rate_limit' => $rateLimit . ' requests per minute'
            ]);
            exit();
        } else {
            $response->error('Method not allowed', 405);
            exit();
        }
    }
    
    // Check API key if required
    if ($config->getBool('require_api_key', false)) {
        $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $_GET['api_key'] ?? null;
        
        if (!$apiKey || !validateApiKey($apiKey)) {
            $response->error('Invalid or missing API key', 401);
            exit();
        }
    }
    
    // Get request body
    $rawInput = file_get_contents('php://input');
    $maxSize = $config->getInt('max_message_size', 10485760); // 10MB default
    
    if (strlen($rawInput) > $maxSize) {
        $response->error('Request body too large', 413);
        exit();
    }
    
    // Parse JSON input
    $inputData = json_decode($rawInput, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        $response->error('Invalid JSON format: ' . json_last_error_msg(), 400);
        exit();
    }
    
    // Extract message data (compatible with Android SMS Gateway format)
    $messageData = extractMessageData($inputData, $_SERVER);
    
    // Validate message data
    $validationErrors = $webhookMessage->validate($messageData);
    if (!empty($validationErrors)) {
        $response->error('Validation failed', 400, ['errors' => $validationErrors]);
        exit();
    }
    
    // Save message to database
    $messageId = $webhookMessage->save($messageData);
    
    if (!$messageId) {
        $response->error('Failed to save message', 500);
        exit();
    }
    
    // Log the request if enabled
    if ($config->getBool('log_all_requests', true)) {
        $processingTime = (microtime(true) - $startTime) * 1000;
        
        $logData = [
            'endpoint' => $_SERVER['REQUEST_URI'] ?? '/api/webhook.php',
            'method' => 'POST',
            'ip_address' => $clientIp,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'request_headers' => getallheaders() ?: [],
            'request_body' => $rawInput,
            'response_code' => 200,
            'response_message' => 'Message received successfully',
            'processing_time_ms' => round($processingTime, 2)
        ];
        
        $webhookLog = new WebhookLog();
        $webhookLog->log($logData);
    }
    
    // Return success response
    $response->success([
        'message' => 'Webhook received successfully',
        'message_id' => $messageData['message_id'],
        'database_id' => $messageId,
        'timestamp' => date('c'),
        'status' => 'received'
    ]);
    
} catch (Exception $e) {
    error_log("Webhook error: " . $e->getMessage());
    $response->error('Internal server error', 500);
}

/**
 * Extract message data from input, compatible with Android SMS Gateway format
 */
function extractMessageData($input, $server) {
    $data = [
        'message_id' => $input['id'] ?? uniqid('msg_', true),
        'sender' => $input['from'] ?? $input['sender'] ?? $input['phone'] ?? 'unknown',
        'message_content' => $input['text'] ?? $input['message'] ?? $input['content'] ?? '',
        'message_type' => $input['type'] ?? 'sms',
        'headers' => getallheaders() ?: [],
        'raw_payload' => $input,
        'ip_address' => $server['REMOTE_ADDR'] ?? null,
        'user_agent' => $server['HTTP_USER_AGENT'] ?? null,
        'status' => 'received'
    ];
    
    // Handle additional fields that might be present
    if (isset($input['timestamp'])) {
        $data['received_at'] = date('Y-m-d H:i:s', $input['timestamp']);
    }
    
    if (isset($input['sim'])) {
        $data['sim_slot'] = $input['sim'];
    }
    
    return $data;
}

/**
 * Validate API key
 */
function validateApiKey($apiKey) {
    try {
        $db = Database::getInstance();
        $sql = "SELECT id FROM api_keys WHERE api_key = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())";
        $result = $db->fetchOne($sql, [$apiKey]);
        
        if ($result) {
            // Update last used timestamp
            $updateSql = "UPDATE api_keys SET last_used_at = NOW() WHERE api_key = ?";
            $db->execute($updateSql, [$apiKey]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("API key validation error: " . $e->getMessage());
        return false;
    }
}
?>
