/**
 * Webhook API Dashboard JavaScript
 */

// Global variables
let currentPage = 1;
let currentFilters = {};

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
    loadMessages();
    loadConfiguration();
    
    // Set up form handlers
    setupFormHandlers();
    
    // Auto-refresh every 30 seconds
    setInterval(loadStatistics, 30000);
});

/**
 * Load and display statistics
 */
async function loadStatistics() {
    try {
        const response = await fetch('api/messages.php?action=stats&days=7');
        const data = await response.json();
        
        if (data.success) {
            const stats = data.data;
            document.getElementById('totalMessages').textContent = stats.total_messages || 0;
            document.getElementById('processedMessages').textContent = stats.processed_messages || 0;
            document.getElementById('pendingMessages').textContent = stats.pending_messages || 0;
            document.getElementById('failedMessages').textContent = stats.failed_messages || 0;
            
            updateConnectionStatus(true);
        } else {
            throw new Error(data.error || 'Failed to load statistics');
        }
    } catch (error) {
        console.error('Error loading statistics:', error);
        updateConnectionStatus(false);
        showAlert('Failed to load statistics: ' + error.message, 'danger');
    }
}

/**
 * Load and display messages
 */
async function loadMessages(page = 1, filters = {}) {
    try {
        showLoading('messagesTableBody');
        
        const params = new URLSearchParams({
            action: 'list',
            page: page,
            limit: 20,
            ...filters
        });
        
        const response = await fetch(`api/messages.php?${params}`);
        const data = await response.json();
        
        if (data.success) {
            displayMessages(data.data);
            displayPagination(data.pagination, 'messagesPagination');
            currentPage = page;
            currentFilters = filters;
        } else {
            throw new Error(data.error || 'Failed to load messages');
        }
    } catch (error) {
        console.error('Error loading messages:', error);
        showAlert('Failed to load messages: ' + error.message, 'danger');
        document.getElementById('messagesTableBody').innerHTML = 
            '<tr><td colspan="7" class="text-center text-danger">Failed to load messages</td></tr>';
    }
}

/**
 * Display messages in table
 */
function displayMessages(messages) {
    const tbody = document.getElementById('messagesTableBody');
    
    if (messages.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No messages found</td></tr>';
        return;
    }
    
    tbody.innerHTML = messages.map(message => `
        <tr>
            <td>${message.id}</td>
            <td>${escapeHtml(message.sender)}</td>
            <td class="message-preview">${escapeHtml(message.message_preview)}</td>
            <td><span class="badge bg-secondary">${message.message_type}</span></td>
            <td><span class="badge ${getStatusBadgeClass(message.status)}">${message.status}</span></td>
            <td>${formatDateTime(message.received_at)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewMessage(${message.id})">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

/**
 * Display pagination
 */
function displayPagination(pagination, containerId) {
    const container = document.getElementById(containerId);
    
    if (pagination.total_pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // Previous button
    html += `<li class="page-item ${!pagination.has_prev ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="loadMessages(${pagination.current_page - 1}, currentFilters)">Previous</a>
    </li>`;
    
    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `<li class="page-item ${i === pagination.current_page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="loadMessages(${i}, currentFilters)">${i}</a>
        </li>`;
    }
    
    // Next button
    html += `<li class="page-item ${!pagination.has_next ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="loadMessages(${pagination.current_page + 1}, currentFilters)">Next</a>
    </li>`;
    
    container.innerHTML = html;
}

/**
 * Load configuration
 */
async function loadConfiguration() {
    try {
        const response = await fetch('api/config.php');
        const data = await response.json();
        
        if (data.success) {
            const config = data.data;
            document.getElementById('webhookUrl').value = config.webhook_url || '';
            document.getElementById('maxMessageSize').value = config.max_message_size || 10485760;
            document.getElementById('rateLimitPerMinute').value = config.rate_limit_per_minute || 100;
            document.getElementById('requireApiKey').checked = config.require_api_key || false;
            document.getElementById('logAllRequests').checked = config.log_all_requests !== false;
            document.getElementById('autoProcessMessages').checked = config.auto_process_messages !== false;
            
            // Set test webhook URL to current webhook URL
            document.getElementById('testWebhookUrl').value = config.webhook_url || '';
        } else {
            throw new Error(data.error || 'Failed to load configuration');
        }
    } catch (error) {
        console.error('Error loading configuration:', error);
        showAlert('Failed to load configuration: ' + error.message, 'danger');
    }
}

/**
 * Setup form handlers
 */
function setupFormHandlers() {
    // Configuration form
    document.getElementById('configForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = {
            webhook_url: document.getElementById('webhookUrl').value,
            max_message_size: document.getElementById('maxMessageSize').value,
            rate_limit_per_minute: document.getElementById('rateLimitPerMinute').value,
            require_api_key: document.getElementById('requireApiKey').checked ? 'true' : 'false',
            log_all_requests: document.getElementById('logAllRequests').checked ? 'true' : 'false',
            auto_process_messages: document.getElementById('autoProcessMessages').checked ? 'true' : 'false'
        };
        
        try {
            const response = await fetch('api/config.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert('Configuration saved successfully', 'success');
            } else {
                throw new Error(data.error || 'Failed to save configuration');
            }
        } catch (error) {
            console.error('Error saving configuration:', error);
            showAlert('Failed to save configuration: ' + error.message, 'danger');
        }
    });
    
    // Test form
    document.getElementById('testForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const testData = {
            webhook_url: document.getElementById('testWebhookUrl').value,
            sender: document.getElementById('testSender').value,
            message: document.getElementById('testMessage').value
        };
        
        try {
            const response = await fetch('api/messages.php?action=test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            });
            
            const data = await response.json();
            
            document.getElementById('testResults').style.display = 'block';
            document.getElementById('testResultsContent').textContent = JSON.stringify(data, null, 2);
            
            if (data.success) {
                showAlert('Test message sent successfully', 'success');
            } else {
                showAlert('Test failed: ' + (data.error || 'Unknown error'), 'warning');
            }
        } catch (error) {
            console.error('Error sending test message:', error);
            showAlert('Failed to send test message: ' + error.message, 'danger');
        }
    });
}

/**
 * Apply filters to message list
 */
function applyFilters() {
    const filters = {};
    
    const sender = document.getElementById('senderFilter').value.trim();
    if (sender) filters.sender = sender;
    
    const status = document.getElementById('statusFilter').value;
    if (status) filters.status = status;
    
    loadMessages(1, filters);
}

/**
 * Refresh messages
 */
function refreshMessages() {
    loadMessages(currentPage, currentFilters);
}

/**
 * View message details
 */
async function viewMessage(messageId) {
    try {
        const response = await fetch(`api/messages.php?action=get&id=${messageId}`);
        const data = await response.json();
        
        if (data.success) {
            const message = data.data;
            const modalBody = document.getElementById('messageModalBody');
            
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>ID:</strong></td><td>${message.id}</td></tr>
                            <tr><td><strong>Message ID:</strong></td><td>${message.message_id}</td></tr>
                            <tr><td><strong>Sender:</strong></td><td>${escapeHtml(message.sender)}</td></tr>
                            <tr><td><strong>Type:</strong></td><td>${message.message_type}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(message.status)}">${message.status}</span></td></tr>
                            <tr><td><strong>Received:</strong></td><td>${formatDateTime(message.received_at)}</td></tr>
                            <tr><td><strong>Processed:</strong></td><td>${message.processed_at ? formatDateTime(message.processed_at) : 'N/A'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Technical Details</h6>
                        <table class="table table-sm">
                            <tr><td><strong>IP Address:</strong></td><td>${message.ip_address || 'N/A'}</td></tr>
                            <tr><td><strong>User Agent:</strong></td><td class="text-break">${escapeHtml(message.user_agent || 'N/A')}</td></tr>
                        </table>
                    </div>
                </div>
                
                <h6>Message Content</h6>
                <div class="bg-light p-3 rounded">
                    <pre>${escapeHtml(message.message_content)}</pre>
                </div>
                
                ${message.error_message ? `
                <h6 class="text-danger">Error Message</h6>
                <div class="alert alert-danger">
                    ${escapeHtml(message.error_message)}
                </div>
                ` : ''}
                
                <h6>Headers</h6>
                <pre class="bg-light p-3 rounded">${JSON.stringify(message.headers, null, 2)}</pre>
                
                <h6>Raw Payload</h6>
                <pre class="bg-light p-3 rounded">${JSON.stringify(message.raw_payload, null, 2)}</pre>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('messageModal'));
            modal.show();
        } else {
            throw new Error(data.error || 'Failed to load message details');
        }
    } catch (error) {
        console.error('Error loading message details:', error);
        showAlert('Failed to load message details: ' + error.message, 'danger');
    }
}

/**
 * Utility functions
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert_' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            const bsAlert = bootstrap.Alert.getOrCreateInstance(alert);
            bsAlert.close();
        }
    }, 5000);
}

function showLoading(containerId) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <tr>
            <td colspan="7" class="text-center">
                <div class="loading show">
                    <i class="fas fa-spinner fa-spin"></i> Loading...
                </div>
            </td>
        </tr>
    `;
}

function updateConnectionStatus(connected) {
    const statusElement = document.getElementById('connectionStatus');
    if (connected) {
        statusElement.innerHTML = '<i class="fas fa-circle text-success me-1"></i>Connected';
    } else {
        statusElement.innerHTML = '<i class="fas fa-circle text-danger me-1"></i>Disconnected';
    }
}

function getStatusBadgeClass(status) {
    switch (status) {
        case 'processed': return 'bg-success';
        case 'failed': return 'bg-danger';
        case 'received': return 'bg-warning';
        default: return 'bg-secondary';
    }
}

function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
