package com.mtc.addmoney;

import android.content.Context;
import android.util.Log;

import androidx.work.Worker;
import androidx.work.WorkerParameters;
import androidx.work.ListenableWorker;

/**
 * WorkManager worker for processing SMS and webhook queues
 */
public class QueueProcessorWorkRequest extends Worker {
    
    private static final String TAG = "QueueProcessorWork";
    
    public QueueProcessorWorkRequest(Context context, WorkerParameters workerParams) {
        super(context, workerParams);
    }
    
    @Override
    public ListenableWorker.Result doWork() {
        String workType = getInputData().getString("WORK_TYPE");
        Log.d(TAG, "Processing work type: " + workType);
        
        try {
            if ("PROCESS_SMS_QUEUE".equals(workType)) {
                processSmsQueue();
            } else {
                // Default: process all queues
                processAllQueues();
            }
            
            return ListenableWorker.Result.success();
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing queue", e);
            return ListenableWorker.Result.failure();
        }
    }
    
    private void processSmsQueue() {
        Log.d(TAG, "Processing SMS queue");
        
        SmsSender smsSender = new SmsSender(getApplicationContext());
        try {
            smsSender.processPendingSms();
        } finally {
            smsSender.cleanup();
        }
    }
    
    private void processAllQueues() {
        Log.d(TAG, "Processing all queues");
        
        // Process SMS queue
        SmsSender smsSender = new SmsSender(getApplicationContext());
        try {
            smsSender.processPendingSms();
        } finally {
            smsSender.cleanup();
        }
        
        // Webhook queue is processed by WebHookWorkRequest when connectivity is available
        // We just trigger the background service to ensure it's running
        BackgroundSmsService.triggerQueueProcessing(getApplicationContext());
    }
}
