package com.mtc.addmoney;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.telephony.SmsMessage;
import android.util.Log;

import androidx.work.BackoffPolicy;
import androidx.work.Constraints;
import androidx.work.Data;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import org.apache.commons.text.StringEscapeUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;

/**
 * Static SMS receiver that works independently of app state
 * This receiver is registered in the manifest and will receive SMS
 * even when the app is closed and mobile data is off
 */
public class StaticSmsReceiver extends BroadcastReceiver {

    private static final String TAG = "StaticSmsReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "SMS received - App state independent");
        
        // Ensure the background SMS service is running
        startBackgroundSmsService(context);
        
        Bundle extras = intent.getExtras();
        if (extras == null) {
            Log.w(TAG, "No extras in SMS intent");
            return;
        }

        Object[] pdus = (Object[]) extras.get("pdus");
        if (pdus == null || pdus.length == 0) {
            Log.w(TAG, "No PDUs in SMS intent");
            return;
        }

        SmsMessage[] messages = new SmsMessage[pdus.length];
        StringBuilder fullMessage = new StringBuilder();

        for (int i = 0; i < pdus.length; i++) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                String format = extras.getString("format");
                messages[i] = SmsMessage.createFromPdu((byte[]) pdus[i], format);
            } else {
                messages[i] = SmsMessage.createFromPdu((byte[]) pdus[i]);
            }
            if (messages[i] != null) {
                fullMessage.append(messages[i].getMessageBody());
            }
        }

        if (messages[0] == null) {
            Log.w(TAG, "Failed to parse SMS message");
            return;
        }

        String sender = messages[0].getOriginatingAddress();
        long sentTime = messages[0].getTimestampMillis();
        long receivedTime = System.currentTimeMillis();
        String simInfo = detectSim(extras);

        Log.i(TAG, "SMS from: " + sender + ", message: " + fullMessage.toString());

        // Store SMS in local database for processing
        SmsQueueManager queueManager = new SmsQueueManager(context);
        queueManager.storeReceivedSms(sender, fullMessage.toString(), sentTime, receivedTime, simInfo);

        // Find matching forwarding configuration
        ForwardingConfig matchedConfig = findMatchingConfig(context, sender);
        if (matchedConfig == null) {
            Log.d(TAG, "No matching forwarding config for sender: " + sender);
            return;
        }

        // Format the JSON payload
        String formattedJson = matchedConfig.getTemplate()
                .replace("%from%", sender)
                .replace("%sentStamp%", String.valueOf(sentTime))
                .replace("%receivedStamp%", String.valueOf(receivedTime))
                .replace("%sim%", simInfo)
                .replace("%text%", Matcher.quoteReplacement(StringEscapeUtils.escapeJson(fullMessage.toString())));

        // Add a small delay to ensure proper processing
        SystemClock.sleep(1000);

        try {
            // Queue the webhook for sending (will be sent when connectivity is available)
            queueWebhookForSending(context, matchedConfig.getUrl(), formattedJson, 
                                 matchedConfig.getHeaders(), matchedConfig.getIgnoreSsl(), sender);
        } catch (Exception e) {
            Log.e(TAG, "Error processing SMS", e);
        }
    }

    private void startBackgroundSmsService(Context context) {
        Intent serviceIntent = new Intent(context, BackgroundSmsService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent);
        } else {
            context.startService(serviceIntent);
        }
    }

    private ForwardingConfig findMatchingConfig(Context context, String sender) {
        List<ForwardingConfig> configs = ForwardingConfig.getAll(context);
        String wildcard = context.getString(R.string.asterisk);

        for (ForwardingConfig config : configs) {
            if (config.getSender().equals(sender) || config.getSender().equals(wildcard)) {
                return config;
            }
        }
        return null;
    }

    private void queueWebhookForSending(Context context, String url, String jsonPayload, 
                                      String headers, boolean ignoreSsl, String sender) {
        // Create notification log entry
        NotificationLogManager logManager = new NotificationLogManager(context);
        NotificationLog log = logManager.createPendingNotification(url, jsonPayload, sender);

        // Check if we have network connectivity
        ConnectivityHelper connectivityHelper = new ConnectivityHelper(context);
        if (connectivityHelper.isConnected()) {
            // Send immediately if connected
            scheduleImmediateWebhook(context, url, jsonPayload, headers, ignoreSsl, log.getId(), sender);
        } else {
            // Queue for later sending when connectivity is restored
            SmsQueueManager queueManager = new SmsQueueManager(context);
            queueManager.queueWebhook(url, jsonPayload, headers, ignoreSsl, log.getId(), sender);
            Log.i(TAG, "Webhook queued for later sending (no connectivity)");
        }
    }

    private void scheduleImmediateWebhook(Context context, String url, String jsonPayload, 
                                        String headers, boolean ignoreSsl, String logId, String sender) {
        Constraints constraints = new Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build();

        Data data = new Data.Builder()
                .putString(WebHookWorkRequest.DATA_URL, url)
                .putString(WebHookWorkRequest.DATA_TEXT, jsonPayload)
                .putString(WebHookWorkRequest.DATA_HEADERS, headers)
                .putBoolean(WebHookWorkRequest.DATA_IGNORE_SSL, ignoreSsl)
                .putString(WebHookWorkRequest.DATA_LOG_ID, logId)
                .putString(WebHookWorkRequest.DATA_SENDER, sender)
                .build();

        OneTimeWorkRequest request = new OneTimeWorkRequest.Builder(WebHookWorkRequest.class)
                .setConstraints(constraints)
                .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, OneTimeWorkRequest.MIN_BACKOFF_MILLIS, TimeUnit.MILLISECONDS)
                .setInputData(data)
                .build();

        WorkManager.getInstance(context).enqueue(request);
    }

    private String detectSim(Bundle extras) {
        if (extras.containsKey("simSlot")) {
            return String.valueOf(extras.get("simSlot"));
        }

        // Try common keys for dual SIM detection
        String[] possibleKeys = {"subscription", "simSlot", "slot", "simId", "phone", "slotId"};

        for (String key : possibleKeys) {
            if (extras.containsKey(key)) {
                return String.valueOf(extras.get(key));
            }
        }

        return "unknown";
    }
}
