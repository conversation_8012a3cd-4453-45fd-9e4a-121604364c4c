<?php
/**
 * API Response Utility Class
 * 
 * Standardizes API responses across the webhook system
 */
class ApiResponse {
    
    /**
     * Send a success response
     */
    public function success($data = null, $message = 'Success', $code = 200) {
        http_response_code($code);

        $response = array(
            'success' => true,
            'message' => $message,
            'timestamp' => date('c')
        );

        if ($data !== null) {
            $response['data'] = $data;
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * Send an error response
     */
    public function error($message = 'Error', $code = 400, $data = null) {
        http_response_code($code);

        $response = array(
            'success' => false,
            'error' => $message,
            'code' => $code,
            'timestamp' => date('c')
        );

        if ($data !== null) {
            $response['data'] = $data;
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * Send a validation error response
     */
    public function validationError($errors, $message = 'Validation failed') {
        $this->error($message, 422, ['validation_errors' => $errors]);
    }
    
    /**
     * Send a paginated response
     */
    public function paginated($data, $total, $page, $limit, $message = 'Success') {
        $totalPages = ceil($total / $limit);
        
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'pagination' => [
                'current_page' => (int)$page,
                'total_pages' => $totalPages,
                'total_items' => (int)$total,
                'items_per_page' => (int)$limit,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ],
            'timestamp' => date('c')
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        exit();
    }
}

?>
    private $storage;
    
    public function __construct() {
        // Use file-based storage for simplicity
        $this->storage = __DIR__ . '/../storage/rate_limits.json';
        $this->ensureStorageExists();
    }
    
    /**
     * Check if the client is within rate limits
     */
    public function checkLimit($clientId, $maxRequests = 100, $windowMinutes = 1) {
        $limits = $this->loadLimits();
        $now = time();
        $windowStart = $now - ($windowMinutes * 60);
        
        // Clean old entries
        $this->cleanOldEntries($limits, $windowStart);
        
        // Count requests in current window
        $clientRequests = $limits[$clientId] ?? [];
        $requestsInWindow = array_filter($clientRequests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        });
        
        if (count($requestsInWindow) >= $maxRequests) {
            return false;
        }
        
        // Add current request
        $limits[$clientId][] = $now;
        $this->saveLimits($limits);
        
        return true;
    }
    
    /**
     * Get current request count for a client
     */
    public function getCurrentCount($clientId, $windowMinutes = 1) {
        $limits = $this->loadLimits();
        $windowStart = time() - ($windowMinutes * 60);
        
        $clientRequests = $limits[$clientId] ?? [];
        return count(array_filter($clientRequests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        }));
    }
    
    /**
     * Reset limits for a client
     */
    public function resetClient($clientId) {
        $limits = $this->loadLimits();
        unset($limits[$clientId]);
        $this->saveLimits($limits);
    }
    
    /**
     * Load rate limits from storage
     */
    private function loadLimits() {
        if (!file_exists($this->storage)) {
            return [];
        }
        
        $content = file_get_contents($this->storage);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * Save rate limits to storage
     */
    private function saveLimits($limits) {
        file_put_contents($this->storage, json_encode($limits));
    }
    
    /**
     * Clean old entries from rate limit storage
     */
    private function cleanOldEntries(&$limits, $windowStart) {
        foreach ($limits as $clientId => &$requests) {
            $requests = array_filter($requests, function($timestamp) use ($windowStart) {
                return $timestamp > $windowStart;
            });
            
            // Remove empty client entries
            if (empty($requests)) {
                unset($limits[$clientId]);
            }
        }
    }
    
    /**
     * Ensure storage directory exists
     */
    private function ensureStorageExists() {
        $dir = dirname($this->storage);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

/**
 * Input Validator Utility Class
 */
class InputValidator {
    
    /**
     * Validate required fields
     */
    public static function required($data, $fields) {
        $errors = [];
        
        foreach ($fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }
        
        return $errors;
    }
    
    /**
     * Validate email format
     */
    public static function email($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate URL format
     */
    public static function url($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Validate phone number (basic)
     */
    public static function phone($phone) {
        return preg_match('/^[\+]?[0-9\s\-\(\)]{10,}$/', $phone);
    }
    
    /**
     * Sanitize string input
     */
    public static function sanitizeString($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate JSON string
     */
    public static function json($jsonString) {
        json_decode($jsonString);
        return json_last_error() === JSON_ERROR_NONE;
    }
}
?>
