<?php
/**
 * Custom Error Page
 * 
 * Handles HTTP error responses with proper security headers
 */

// Get error code from query parameter
$errorCode = $_GET['code'] ?? '500';
$errorCode = (int)$errorCode;

// Set appropriate HTTP status code
http_response_code($errorCode);

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Error messages
$errorMessages = [
    400 => 'Bad Request',
    401 => 'Unauthorized',
    403 => 'Forbidden',
    404 => 'Not Found',
    405 => 'Method Not Allowed',
    413 => 'Request Entity Too Large',
    429 => 'Too Many Requests',
    500 => 'Internal Server Error',
    502 => 'Bad Gateway',
    503 => 'Service Unavailable'
];

$errorTitle = $errorMessages[$errorCode] ?? 'Error';
$errorDescription = getErrorDescription($errorCode);

// Check if this is an API request
$isApiRequest = (
    strpos($_SERVER['REQUEST_URI'] ?? '', '/api/') !== false ||
    strpos($_SERVER['HTTP_ACCEPT'] ?? '', 'application/json') !== false ||
    strpos($_SERVER['CONTENT_TYPE'] ?? '', 'application/json') !== false
);

if ($isApiRequest) {
    // Return JSON error response for API requests
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $errorTitle,
        'code' => $errorCode,
        'message' => $errorDescription,
        'timestamp' => date('c')
    ], JSON_PRETTY_PRINT);
    exit;
}

// Return HTML error page for web requests
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $errorCode; ?> - <?php echo htmlspecialchars($errorTitle); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .error-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 1rem;
        }
        .error-description {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <?php echo getErrorIcon($errorCode); ?>
        </div>
        <div class="error-code"><?php echo $errorCode; ?></div>
        <h1 class="error-title"><?php echo htmlspecialchars($errorTitle); ?></h1>
        <p class="error-description"><?php echo htmlspecialchars($errorDescription); ?></p>
        
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="index.html" class="btn-home">
                <i class="fas fa-home me-2"></i>Go to Dashboard
            </a>
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Go Back
            </a>
        </div>
        
        <?php if ($errorCode === 404): ?>
        <div class="mt-4">
            <small class="text-muted">
                If you believe this is an error, please check the URL or contact the administrator.
            </small>
        </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>

<?php

function getErrorDescription($code) {
    switch ($code) {
        case 400:
            return 'The request could not be understood by the server due to malformed syntax.';
        case 401:
            return 'Authentication is required to access this resource.';
        case 403:
            return 'You do not have permission to access this resource.';
        case 404:
            return 'The requested resource could not be found on this server.';
        case 405:
            return 'The request method is not allowed for this resource.';
        case 413:
            return 'The request is larger than the server is willing or able to process.';
        case 429:
            return 'Too many requests have been made in a short period of time. Please try again later.';
        case 500:
            return 'The server encountered an unexpected condition that prevented it from fulfilling the request.';
        case 502:
            return 'The server received an invalid response from an upstream server.';
        case 503:
            return 'The server is temporarily unavailable due to maintenance or overload.';
        default:
            return 'An error occurred while processing your request.';
    }
}

function getErrorIcon($code) {
    switch ($code) {
        case 400:
        case 422:
            return '⚠️';
        case 401:
        case 403:
            return '🔒';
        case 404:
            return '🔍';
        case 405:
            return '🚫';
        case 413:
            return '📦';
        case 429:
            return '⏱️';
        case 500:
        case 502:
        case 503:
            return '🔧';
        default:
            return '❌';
    }
}
?>
