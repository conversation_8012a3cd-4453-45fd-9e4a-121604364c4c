package com.mtc.addmoney;

import android.Manifest;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private static final int PERMISSION_CODE = 0;
    private static final int NOTIFICATION_PERMISSION_CODE = 1;
    private Context context;
    private AlertDialog alertDialog;

    // Tab-related components
    private TabLayout tabLayout;
    private ViewPager2 viewPager;
    private MainPagerAdapter pagerAdapter;
    private FloatingActionButton btnAdd;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main_with_tabs);

        context = this;

        // Setup Toolbar as ActionBar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setTitleTextColor(getResources().getColor(android.R.color.white));

        setupTabs();
        setupFloatingActionButton();

        // Check permissions
        checkPermissions();
    }

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.RECEIVE_SMS)
                != PackageManager.PERMISSION_GRANTED ||
                ContextCompat.checkSelfPermission(this, android.Manifest.permission.SEND_SMS)
                != PackageManager.PERMISSION_GRANTED ||
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
                        ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS)
                                != PackageManager.PERMISSION_GRANTED)) {

            java.util.List<String> permissionsList = new java.util.ArrayList<>();
            permissionsList.add(android.Manifest.permission.RECEIVE_SMS);
            permissionsList.add(android.Manifest.permission.SEND_SMS);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                permissionsList.add(android.Manifest.permission.POST_NOTIFICATIONS);
            }

            String[] permissions = permissionsList.toArray(new String[0]);
            ActivityCompat.requestPermissions(this, permissions, PERMISSION_CODE);
        } else {
            setupAfterPermissions();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == PERMISSION_CODE) {
            boolean allGranted = true;
            for (int i = 0; i < permissions.length; i++) {
                if (android.Manifest.permission.RECEIVE_SMS.equals(permissions[i]) ||
                        (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
                                android.Manifest.permission.POST_NOTIFICATIONS.equals(permissions[i]))) {
                    if (grantResults[i] != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                        allGranted = false;
                        if (android.Manifest.permission.RECEIVE_SMS.equals(permissions[i])) {
                            showInfo(getString(R.string.permission_needed));
                        } else if (android.Manifest.permission.POST_NOTIFICATIONS.equals(permissions[i])) {
                            showInfo(getString(R.string.notification_permission_needed));
                        }
                        break;
                    }
                }
            }
            if (allGranted) {
                setupAfterPermissions();
            }
        } else {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    private void setupTabs() {
        tabLayout = findViewById(R.id.tab_layout);
        viewPager = findViewById(R.id.view_pager);

        pagerAdapter = new MainPagerAdapter(this);
        viewPager.setAdapter(pagerAdapter);

        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case MainPagerAdapter.TAB_WEBHOOKS:
                    tab.setText(getString(R.string.tab_webhooks));
                    break;
                case MainPagerAdapter.TAB_MONITOR:
                    tab.setText(getString(R.string.tab_monitor));
                    break;
            }
        }).attach();
    }

    private void setupFloatingActionButton() {
        btnAdd = findViewById(R.id.btn_add);
        btnAdd.setOnClickListener(v -> showAddDialog());
    }

    private void setupAfterPermissions() {
        showInfo("");

        // Start the original SMS receiver service
        if (!isServiceRunning()) {
            startService();
        }

        // Start the new background SMS service
        startBackgroundSmsService();

        // Check and request battery optimization whitelist
        checkBatteryOptimization();
    }

    private void startBackgroundSmsService() {
        Intent intent = new Intent(this, BackgroundSmsService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent);
        } else {
            startService(intent);
        }
    }

    private void checkBatteryOptimization() {
        // Show battery optimization dialog after a short delay to avoid overwhelming the user
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            BatteryOptimizationHelper.showBatteryOptimizationDialog(this);
        }, 2000);
    }

    private boolean isServiceRunning() {
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager == null) return false;

        for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (SmsReceiverService.class.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    private void startService() {
        Intent intent = new Intent(this, SmsReceiverService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            getApplicationContext().startForegroundService(intent);
        } else {
            getApplicationContext().startService(intent);
        }
    }

    private void showInfo(String message) {
        TextView infoNotice = findViewById(R.id.info_notice);
        infoNotice.setText(message);
    }

    private void showAddDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_add, null);

        EditText inputPhone = dialogView.findViewById(R.id.input_phone);
        EditText inputUrl = dialogView.findViewById(R.id.input_url);
        EditText inputJsonTemplate = dialogView.findViewById(R.id.input_json_template);
        EditText inputJsonHeaders = dialogView.findViewById(R.id.input_json_headers);
        CheckBox inputIgnoreSsl = dialogView.findViewById(R.id.input_ignore_ssl);
        Button btnAdd = dialogView.findViewById(R.id.btn_add);
        Button btnCancel = dialogView.findViewById(R.id.btn_cancel);

        inputJsonTemplate.setText(ForwardingConfig.getDefaultJsonTemplate());
        inputJsonHeaders.setText(ForwardingConfig.getDefaultJsonHeaders());

        builder.setView(dialogView);

        AlertDialog alertDialog = builder.create();
        alertDialog.getWindow().setSoftInputMode(16);
        alertDialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

        alertDialog.show();

        btnAdd.setOnClickListener(v -> {
            try {
                String sender = inputPhone.getText().toString().trim();
                if (TextUtils.isEmpty(sender)) {
                    inputPhone.setError(getString(R.string.error_empty_sender));
                    return;
                }

                String urlStr = inputUrl.getText().toString().trim();
                if (TextUtils.isEmpty(urlStr)) {
                    inputUrl.setError(getString(R.string.error_empty_url));
                    return;
                }
                new URL(urlStr); // Validate URL format

                String jsonTemplate = inputJsonTemplate.getText().toString().trim();
                try {
                    new JSONObject(jsonTemplate); // Validate JSON template
                } catch (JSONException e) {
                    inputJsonTemplate.setError(getString(R.string.error_wrong_json));
                    return;
                }

                String jsonHeaders = inputJsonHeaders.getText().toString().trim();
                try {
                    new JSONObject(jsonHeaders); // Validate JSON headers
                } catch (JSONException e) {
                    inputJsonHeaders.setError(getString(R.string.error_wrong_json));
                    return;
                }

                boolean ignoreSsl = inputIgnoreSsl.isChecked();

                Log.d(TAG, "Creating new ForwardingConfig with sender: " + sender + ", url: " + urlStr);

                ForwardingConfig forwardingConfig = new ForwardingConfig(context);
                forwardingConfig.setSender(sender);
                forwardingConfig.setUrl(urlStr);
                forwardingConfig.setTemplate(jsonTemplate);
                forwardingConfig.setHeaders(jsonHeaders);
                forwardingConfig.setIgnoreSsl(ignoreSsl);

                Log.d(TAG, "Saving ForwardingConfig...");
                forwardingConfig.save();
                Log.d(TAG, "ForwardingConfig saved successfully");

                // Refresh the webhooks fragment
                Log.d(TAG, "Attempting to refresh webhooks list...");
                if (pagerAdapter != null) {
                    Log.d(TAG, "PagerAdapter is not null");

                    // Ensure the WebhooksFragment is created
                    pagerAdapter.ensureWebhooksFragmentCreated();

                    WebhooksFragment webhooksFragment = pagerAdapter.getWebhooksFragment();
                    if (webhooksFragment != null) {
                        Log.d(TAG, "WebhooksFragment found, calling refreshList()");
                        webhooksFragment.refreshList();
                        Log.d(TAG, "refreshList() called successfully");

                        // Switch to webhooks tab to show the updated list
                        if (viewPager != null) {
                            Log.d(TAG, "Switching to webhooks tab to show updated list");
                            viewPager.setCurrentItem(MainPagerAdapter.TAB_WEBHOOKS, true);
                        }
                    } else {
                        Log.e(TAG, "WebhooksFragment is still null after ensuring creation");
                    }
                } else {
                    Log.e(TAG, "PagerAdapter is null");
                }

                alertDialog.dismiss();

            } catch (MalformedURLException e) {
                inputUrl.setError(getString(R.string.error_wrong_url));
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        });

        btnCancel.setOnClickListener(v -> alertDialog.dismiss());
    }
}