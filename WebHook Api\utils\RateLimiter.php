<?php
/**
 * Rate Limiter Utility Class
 * 
 * Implements simple rate limiting based on IP address
 */
class RateLimiter {
    private $storage;
    
    public function __construct() {
        // Use file-based storage for simplicity
        $this->storage = __DIR__ . '/../storage/rate_limits.json';
        $this->ensureStorageExists();
    }
    
    /**
     * Check if the client is within rate limits
     */
    public function checkLimit($clientId, $maxRequests = 100, $windowMinutes = 1) {
        $limits = $this->loadLimits();
        $now = time();
        $windowStart = $now - ($windowMinutes * 60);
        
        // Clean old entries
        $this->cleanOldEntries($limits, $windowStart);
        
        // Count requests in current window
        $clientRequests = $limits[$clientId] ?? [];
        $requestsInWindow = array_filter($clientRequests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        });
        
        if (count($requestsInWindow) >= $maxRequests) {
            return false;
        }
        
        // Add current request
        $limits[$clientId][] = $now;
        $this->saveLimits($limits);
        
        return true;
    }
    
    /**
     * Get current request count for a client
     */
    public function getCurrentCount($clientId, $windowMinutes = 1) {
        $limits = $this->loadLimits();
        $windowStart = time() - ($windowMinutes * 60);
        
        $clientRequests = $limits[$clientId] ?? [];
        return count(array_filter($clientRequests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        }));
    }
    
    /**
     * Reset limits for a client
     */
    public function resetClient($clientId) {
        $limits = $this->loadLimits();
        unset($limits[$clientId]);
        $this->saveLimits($limits);
    }
    
    /**
     * Load rate limits from storage
     */
    private function loadLimits() {
        if (!file_exists($this->storage)) {
            return [];
        }
        
        $content = file_get_contents($this->storage);
        return json_decode($content, true) ?: [];
    }
    
    /**
     * Save rate limits to storage
     */
    private function saveLimits($limits) {
        file_put_contents($this->storage, json_encode($limits));
    }
    
    /**
     * Clean old entries from rate limit storage
     */
    private function cleanOldEntries(&$limits, $windowStart) {
        foreach ($limits as $clientId => &$requests) {
            $requests = array_filter($requests, function($timestamp) use ($windowStart) {
                return $timestamp > $windowStart;
            });
            
            // Remove empty client entries
            if (empty($requests)) {
                unset($limits[$clientId]);
            }
        }
    }
    
    /**
     * Ensure storage directory exists
     */
    private function ensureStorageExists() {
        $dir = dirname($this->storage);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}
?>
