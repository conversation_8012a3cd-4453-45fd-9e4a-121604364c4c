package com.mtc.addmoney;

import android.app.AlertDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Fragment for monitoring webhook notifications
 */
public class MonitorFragment extends Fragment {

    private RecyclerView recyclerView;
    private NotificationLogAdapter adapter;
    private LinearLayout emptyStateLayout;
    private TextView totalCountText, successCountText, failedCountText, pendingCountText;
    private Spinner statusFilterSpinner;
    private Button refreshButton, clearLogsButton;
    
    private NotificationLogManager logManager;
    private String currentFilter = "all";

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_monitor, container, false);
        
        initViews(view);
        setupRecyclerView();
        setupFilterSpinner();
        setupButtons();
        
        logManager = new NotificationLogManager(requireContext());
        
        loadData();
        
        return view;
    }

    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.recycler_view_notifications);
        emptyStateLayout = view.findViewById(R.id.layout_empty_state);
        
        // Statistics TextViews
        totalCountText = view.findViewById(R.id.text_total_count);
        successCountText = view.findViewById(R.id.text_success_count);
        failedCountText = view.findViewById(R.id.text_failed_count);
        pendingCountText = view.findViewById(R.id.text_pending_count);
        
        // Controls
        statusFilterSpinner = view.findViewById(R.id.spinner_status_filter);
        refreshButton = view.findViewById(R.id.btn_refresh);
        clearLogsButton = view.findViewById(R.id.btn_clear_logs);
    }

    private void setupRecyclerView() {
        adapter = new NotificationLogAdapter(new ArrayList<>());
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerView.setAdapter(adapter);
    }

    private void setupFilterSpinner() {
        List<String> filterOptions = Arrays.asList(
            getString(R.string.filter_all),
            getString(R.string.filter_success),
            getString(R.string.filter_failed),
            getString(R.string.filter_pending),
            getString(R.string.filter_retry)
        );
        
        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(
            requireContext(),
            android.R.layout.simple_spinner_item,
            filterOptions
        );
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusFilterSpinner.setAdapter(spinnerAdapter);
        
        statusFilterSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                switch (position) {
                    case 0:
                        currentFilter = "all";
                        break;
                    case 1:
                        currentFilter = NotificationLog.STATUS_SUCCESS;
                        break;
                    case 2:
                        currentFilter = NotificationLog.STATUS_FAILED;
                        break;
                    case 3:
                        currentFilter = NotificationLog.STATUS_PENDING;
                        break;
                    case 4:
                        currentFilter = NotificationLog.STATUS_RETRY;
                        break;
                }
                loadData();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Do nothing
            }
        });
    }

    private void setupButtons() {
        refreshButton.setOnClickListener(v -> {
            loadData();
            Toast.makeText(requireContext(), "Refreshed", Toast.LENGTH_SHORT).show();
        });
        
        clearLogsButton.setOnClickListener(v -> showClearLogsDialog());
    }

    private void showClearLogsDialog() {
        new AlertDialog.Builder(requireContext())
            .setTitle("Clear Logs")
            .setMessage(getString(R.string.confirm_clear_logs))
            .setPositiveButton("Clear", (dialog, which) -> {
                logManager.clearAllLogs();
                loadData();
                Toast.makeText(requireContext(), getString(R.string.logs_cleared), Toast.LENGTH_SHORT).show();
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void loadData() {
        // Load statistics
        NotificationLogManager.NotificationStats stats = logManager.getStats();
        updateStatistics(stats);
        
        // Load filtered notification logs
        ArrayList<NotificationLog> logs;
        if ("all".equals(currentFilter)) {
            logs = logManager.getAllNotifications();
        } else {
            logs = logManager.getNotificationsByStatus(currentFilter);
        }
        
        // Update UI
        if (logs.isEmpty()) {
            recyclerView.setVisibility(View.GONE);
            emptyStateLayout.setVisibility(View.VISIBLE);
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            emptyStateLayout.setVisibility(View.GONE);
            adapter.updateLogs(logs);
        }
    }

    private void updateStatistics(NotificationLogManager.NotificationStats stats) {
        totalCountText.setText(String.valueOf(stats.totalCount));
        successCountText.setText(String.valueOf(stats.successCount));
        failedCountText.setText(String.valueOf(stats.failedCount));
        pendingCountText.setText(String.valueOf(stats.pendingCount));
    }

    /**
     * Public method to refresh data from outside the fragment
     */
    public void refreshData() {
        if (logManager != null) {
            loadData();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Refresh data when fragment becomes visible
        if (logManager != null) {
            loadData();
        }
    }
}
